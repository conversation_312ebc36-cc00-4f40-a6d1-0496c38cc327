#!/usr/bin/env node

/**
 * Test script to simulate automation script sending token to API
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:9043';
const AUTH_TOKEN = 'your_secret_password_here_change_this';

async function testTokenSave() {
  console.log('🧪 Testing token save with console output...\n');

  try {
    // Simulate a token from automation script
    const tokenData = {
      access_token: 'test_token_' + Date.now() + '_abcdef123456789',
      tenant_url: 'https://d1.api.augmentcode.com/',
      description: 'Test token from automation script',
      email_note: '<EMAIL>',
      user_agent: 'firefox-fixed-automation',
      session_id: `firefox_fixed_${Date.now()}`,
      created_timestamp: Date.now()
    };

    console.log('Sending token to API...');
    const response = await axios.post(`${BASE_URL}/api/tokens/save`, 
      tokenData, 
      { 
        headers: { 
          'Authorization': `Bearer ${AUTH_TOKEN}`, 
          'Content-Type': 'application/json' 
        } 
      }
    );
    
    console.log(`✅ ${response.data.message}`);
    console.log(`Token ID: ${response.data.tokenId}`);
    console.log('\n📝 Check server console for detailed token information display');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testTokenSave();
