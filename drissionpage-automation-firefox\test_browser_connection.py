#!/usr/bin/env python3
"""
DrissionPage Browser Connection Test
测试DrissionPage浏览器连接是否正常
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

try:
    from DrissionPage import ChromiumPage, ChromiumOptions
    print("✅ DrissionPage导入成功")
except ImportError as e:
    print(f"❌ DrissionPage导入失败: {e}")
    print("💡 请运行: pip3 install DrissionPage>=4.0.0")
    sys.exit(1)

def test_browser_connection():
    """测试浏览器连接"""
    print("🔧 开始测试DrissionPage浏览器连接...")
    
    try:
        # 创建浏览器选项
        options = ChromiumOptions()
        
        # 添加Ubuntu兼容的启动参数
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-setuid-sandbox')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-gpu')
        options.set_argument('--no-first-run')
        options.set_argument('--no-zygote')
        options.set_argument('--disable-background-timer-throttling')
        options.set_argument('--disable-backgrounding-occluded-windows')
        options.set_argument('--disable-renderer-backgrounding')
        
        # 如果是远程连接，添加这些参数
        if not os.environ.get('DISPLAY'):
            print("⚠️ 未检测到DISPLAY环境变量，添加headless模式")
            options.set_argument('--headless')
        
        print("🚀 尝试启动浏览器...")
        
        # 创建页面实例
        page = ChromiumPage(addr_or_opts=options)
        
        print("✅ 浏览器启动成功！")
        
        # 测试导航到一个简单页面
        print("🌐 测试页面导航...")
        page.get('https://www.google.com')
        
        print("✅ 页面导航成功！")
        print(f"📄 当前页面标题: {page.title}")
        
        # 关闭浏览器
        page.quit()
        print("✅ 浏览器关闭成功！")
        
        print("\n🎉 DrissionPage浏览器连接测试完全成功！")
        print("💡 现在可以运行您的自动化脚本了")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 浏览器连接测试失败: {str(e)}")
        print("\n🔍 可能的解决方案:")
        print("1. 确保已安装Chrome: sudo apt install google-chrome-stable")
        print("2. 安装系统依赖: sudo apt install libgconf-2-4 libxss1 libappindicator1")
        print("3. 设置DISPLAY变量: export DISPLAY=:0")
        print("4. 检查权限: 确保当前用户有运行Chrome的权限")
        
        return False

def check_system_info():
    """检查系统信息"""
    print("🔍 系统环境检查:")
    
    # 检查操作系统
    import platform
    print(f"   🖥️ 操作系统: {platform.system()} {platform.release()}")
    
    # 检查Python版本
    print(f"   🐍 Python版本: {sys.version}")
    
    # 检查DISPLAY环境变量
    display = os.environ.get('DISPLAY', '未设置')
    print(f"   📺 DISPLAY环境变量: {display}")
    
    # 检查Chrome是否安装
    chrome_paths = [
        '/usr/bin/google-chrome',
        '/usr/bin/google-chrome-stable',
        '/usr/bin/chromium-browser',
        '/usr/bin/chromium',
        '/snap/bin/chromium'
    ]
    
    chrome_found = False
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"   ✅ 找到Chrome: {path}")
            chrome_found = True
            break
    
    if not chrome_found:
        print("   ❌ 未找到Chrome/Chromium浏览器")
        print("   💡 请安装: sudo apt install google-chrome-stable")
    
    print()

if __name__ == '__main__':
    print("🧪 DrissionPage浏览器连接测试工具")
    print("=" * 50)
    
    check_system_info()
    test_browser_connection()
