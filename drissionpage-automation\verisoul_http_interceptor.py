"""
Verisoul HTTP 拦截器
通过拦截和伪造 HTTP 响应来欺骗 Verisoul 检测
比逆向分析更简单直接的方案
"""

import json
import time
import uuid
from typing import Dict, Any, Optional

class VerisoulHttpInterceptor:
    """Verisoul HTTP 拦截和欺骗系统"""
    
    def __init__(self, page, logger=None):
        self.page = page
        self.logger = logger
        self.intercepted_requests = []
        self.fake_responses = {}
        self.is_active = False
        
    def activate_interception(self):
        """激活 HTTP 拦截"""
        self.logger.log('🎯 激活 Verisoul HTTP 拦截器...')
        
        try:
            # 注入拦截脚本
            self.inject_interception_script()
            self.is_active = True
            self.logger.log('✅ Verisoul HTTP 拦截器已激活')
            return True
        except Exception as e:
            self.logger.error('❌ 激活 HTTP 拦截器失败', e)
            return False
    
    def inject_interception_script(self):
        """注入 HTTP 拦截脚本"""
        interception_script = """
        // Verisoul HTTP 拦截器
        window.verisoulHttpInterceptor = {
            originalFetch: window.fetch,
            originalXHR: window.XMLHttpRequest,
            interceptedRequests: [],
            fakeResponses: {},
            
            // 激活拦截
            activate: function() {
                console.log('🎯 激活 Verisoul HTTP 拦截...');
                this.interceptFetch();
                this.interceptXHR();
                this.interceptWebSocket();
                console.log('✅ HTTP 拦截已激活');
            },
            
            // 拦截 fetch 请求
            interceptFetch: function() {
                const self = this;
                window.fetch = function(url, options) {
                    // 检查是否是 Verisoul 相关请求
                    if (self.isVerisoulRequest(url)) {
                        console.log('🚫 拦截 Verisoul fetch 请求:', url);
                        self.interceptedRequests.push({
                            type: 'fetch',
                            url: url,
                            options: options,
                            timestamp: Date.now()
                        });
                        
                        // 返回伪造的成功响应
                        return self.createFakeResponse(url, options);
                    }
                    
                    // 非 Verisoul 请求正常处理
                    return self.originalFetch.apply(this, arguments);
                };
            },
            
            // 拦截 XMLHttpRequest
            interceptXHR: function() {
                const self = this;
                const OriginalXHR = this.originalXHR;
                
                window.XMLHttpRequest = function() {
                    const xhr = new OriginalXHR();
                    const originalOpen = xhr.open;
                    const originalSend = xhr.send;
                    
                    xhr.open = function(method, url, async, user, password) {
                        this._url = url;
                        this._method = method;
                        
                        if (self.isVerisoulRequest(url)) {
                            console.log('🚫 拦截 Verisoul XHR 请求:', url);
                            self.interceptedRequests.push({
                                type: 'xhr',
                                method: method,
                                url: url,
                                timestamp: Date.now()
                            });
                            
                            // 伪造成功响应
                            setTimeout(() => {
                                this.readyState = 4;
                                this.status = 200;
                                this.statusText = 'OK';
                                this.responseText = self.getFakeResponseText(url);
                                this.response = this.responseText;
                                
                                if (this.onreadystatechange) {
                                    this.onreadystatechange();
                                }
                                if (this.onload) {
                                    this.onload();
                                }
                            }, 100 + Math.random() * 200);
                            
                            return;
                        }
                        
                        return originalOpen.apply(this, arguments);
                    };
                    
                    xhr.send = function(data) {
                        if (self.isVerisoulRequest(this._url)) {
                            // Verisoul 请求不实际发送
                            return;
                        }
                        return originalSend.apply(this, arguments);
                    };
                    
                    return xhr;
                };
            },
            
            // 拦截 WebSocket
            interceptWebSocket: function() {
                const self = this;
                const OriginalWebSocket = window.WebSocket;
                
                window.WebSocket = function(url, protocols) {
                    if (self.isVerisoulRequest(url)) {
                        console.log('🚫 拦截 Verisoul WebSocket 连接:', url);
                        self.interceptedRequests.push({
                            type: 'websocket',
                            url: url,
                            protocols: protocols,
                            timestamp: Date.now()
                        });
                        
                        // 创建伪造的 WebSocket 对象
                        return self.createFakeWebSocket(url);
                    }
                    
                    return new OriginalWebSocket(url, protocols);
                };
            },
            
            // 检查是否是 Verisoul 请求
            isVerisoulRequest: function(url) {
                if (!url) return false;
                const verisoulDomains = [
                    'verisoul.ai',
                    'net.prod.verisoul.ai',
                    'net1.prod.verisoul.ai',
                    'ingest.prod.verisoul.ai',
                    'api.verisoul.ai'
                ];
                
                return verisoulDomains.some(domain => url.includes(domain));
            },
            
            // 创建伪造的 fetch 响应
            createFakeResponse: function(url, options) {
                const responseData = this.getFakeResponseData(url);
                
                return Promise.resolve({
                    ok: true,
                    status: 200,
                    statusText: 'OK',
                    headers: new Headers({
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    }),
                    json: () => Promise.resolve(responseData),
                    text: () => Promise.resolve(JSON.stringify(responseData)),
                    blob: () => Promise.resolve(new Blob([JSON.stringify(responseData)])),
                    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
                    clone: function() { return this; }
                });
            },
            
            // 创建伪造的 WebSocket
            createFakeWebSocket: function(url) {
                const fakeWS = {
                    url: url,
                    readyState: 1, // OPEN
                    CONNECTING: 0,
                    OPEN: 1,
                    CLOSING: 2,
                    CLOSED: 3,
                    
                    send: function(data) {
                        console.log('📤 伪造 WebSocket 发送:', data);
                        // 模拟服务器响应
                        setTimeout(() => {
                            if (this.onmessage) {
                                const fakeResponse = {
                                    data: JSON.stringify({
                                        type: 'response',
                                        success: true,
                                        session_id: 'fake_' + Math.random().toString(36).substr(2, 9)
                                    })
                                };
                                this.onmessage(fakeResponse);
                            }
                        }, 50 + Math.random() * 100);
                    },
                    
                    close: function() {
                        this.readyState = 3; // CLOSED
                        if (this.onclose) {
                            this.onclose({ code: 1000, reason: 'Normal closure' });
                        }
                    },
                    
                    addEventListener: function(type, listener) {
                        this['on' + type] = listener;
                    },
                    
                    removeEventListener: function(type, listener) {
                        this['on' + type] = null;
                    }
                };
                
                // 模拟连接成功
                setTimeout(() => {
                    if (fakeWS.onopen) {
                        fakeWS.onopen({ type: 'open' });
                    }
                }, 10);
                
                return fakeWS;
            },
            
            // 获取伪造的响应数据
            getFakeResponseData: function(url) {
                if (url.includes('/session') || url.includes('/identify')) {
                    return {
                        success: true,
                        session_id: 'fake_session_' + Math.random().toString(36).substr(2, 9),
                        user_id: 'fake_user_' + Math.random().toString(36).substr(2, 9),
                        risk_score: 0.1, // 低风险分数
                        bot_score: 0.05, // 低机器人分数
                        confidence: 0.95,
                        timestamp: Date.now(),
                        status: 'verified'
                    };
                }
                
                if (url.includes('/track') || url.includes('/event')) {
                    return {
                        success: true,
                        event_id: 'fake_event_' + Math.random().toString(36).substr(2, 9),
                        processed: true,
                        timestamp: Date.now()
                    };
                }
                
                if (url.includes('/verify') || url.includes('/validate')) {
                    return {
                        success: true,
                        verified: true,
                        risk_level: 'low',
                        confidence: 0.98,
                        session_valid: true,
                        timestamp: Date.now()
                    };
                }
                
                // 默认成功响应
                return {
                    success: true,
                    status: 'ok',
                    message: 'Request processed successfully',
                    timestamp: Date.now()
                };
            },
            
            // 获取伪造的响应文本
            getFakeResponseText: function(url) {
                return JSON.stringify(this.getFakeResponseData(url));
            },
            
            // 获取拦截统计
            getInterceptionStats: function() {
                return {
                    totalIntercepted: this.interceptedRequests.length,
                    byType: this.interceptedRequests.reduce((acc, req) => {
                        acc[req.type] = (acc[req.type] || 0) + 1;
                        return acc;
                    }, {}),
                    recentRequests: this.interceptedRequests.slice(-10)
                };
            },
            
            // 停用拦截
            deactivate: function() {
                console.log('🛑 停用 Verisoul HTTP 拦截...');
                window.fetch = this.originalFetch;
                window.XMLHttpRequest = this.originalXHR;
                console.log('✅ HTTP 拦截已停用');
            }
        };
        
        // 自动激活拦截
        window.verisoulHttpInterceptor.activate();
        console.log('🎯 Verisoul HTTP 拦截器已注入并激活');
        """
        
        self.page.run_js(interception_script)
    
    def get_interception_stats(self) -> Optional[Dict]:
        """获取拦截统计信息"""
        try:
            stats = self.page.run_js('return window.verisoulHttpInterceptor.getInterceptionStats();')
            return stats
        except Exception as e:
            self.logger.error('❌ 获取拦截统计失败', e)
            return None
    
    def add_custom_response(self, url_pattern: str, response_data: Dict[str, Any]):
        """添加自定义响应"""
        self.fake_responses[url_pattern] = response_data
        
        try:
            # 更新页面中的伪造响应
            self.page.run_js(f"""
                window.verisoulHttpInterceptor.fakeResponses['{url_pattern}'] = {json.dumps(response_data)};
            """)
            self.logger.log(f'✅ 已添加自定义响应: {url_pattern}')
        except Exception as e:
            self.logger.error('❌ 添加自定义响应失败', e)
    
    def simulate_successful_verification(self):
        """模拟成功的验证流程"""
        self.logger.log('🎭 模拟 Verisoul 成功验证...')
        
        # 添加常见的成功响应
        success_responses = {
            '/session': {
                'success': True,
                'session_id': f'success_session_{uuid.uuid4().hex[:16]}',
                'risk_score': 0.05,
                'bot_score': 0.02,
                'confidence': 0.98,
                'status': 'verified',
                'user_verified': True
            },
            '/verify': {
                'success': True,
                'verified': True,
                'risk_level': 'very_low',
                'confidence': 0.99,
                'session_valid': True,
                'human_probability': 0.98
            },
            '/track': {
                'success': True,
                'processed': True,
                'event_recorded': True
            }
        }
        
        for pattern, response in success_responses.items():
            self.add_custom_response(pattern, response)
    
    def deactivate_interception(self):
        """停用拦截"""
        if not self.is_active:
            return
            
        try:
            self.page.run_js('window.verisoulHttpInterceptor.deactivate();')
            self.is_active = False
            self.logger.log('🛑 Verisoul HTTP 拦截器已停用')
        except Exception as e:
            self.logger.error('❌ 停用 HTTP 拦截器失败', e)
    
    def get_status(self) -> Dict[str, Any]:
        """获取拦截器状态"""
        stats = self.get_interception_stats() or {}
        
        return {
            'is_active': self.is_active,
            'total_intercepted': stats.get('totalIntercepted', 0),
            'interception_by_type': stats.get('byType', {}),
            'custom_responses': len(self.fake_responses)
        }
