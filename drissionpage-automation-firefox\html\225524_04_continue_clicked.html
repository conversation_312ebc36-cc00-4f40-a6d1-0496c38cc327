
<!-- DEBUG INFO -->
<!-- Step: 04_continue_clicked -->
<!-- Description: Continue按钮点击完成 -->
<!-- URL: https://login.augmentcode.com/u/login/passwordless-email-challenge?state=hKFo2SBIQTdsc2MzWWpBbzJUbTNKUHlxYkZXdjctdnFyVnNrdqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGZWWmJaNUZHeWNOS1NyTWNzMDFOTDd3S0JOWDFmLWtOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE -->
<!-- Timestamp: 2025-08-18T22:55:24.273368 -->
<!-- END DEBUG INFO -->

<html lang="en"><head>    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
      <meta name="ulp-version" content="1.70.0">
    
    
    
    <meta name="robots" content="noindex, nofollow">
    
    
    <link rel="stylesheet" href="https://cdn.auth0.com/ulp/react-components/1.147.0/css/main.cdn.min.css">
    <style id="custom-styles-container">
      
        




        
          :root, .af-custom-form-container .af-form {
    --primary-color: #233A3E;
  }
        
      

        
          :root, .af-custom-form-container .af-form {
    --button-font-color: #ffffff;
  }
        
      

        
          :root {
    --secondary-button-border-color: #BBB8B4;
    --social-button-border-color: #BBB8B4;
    --radio-button-border-color: #BBB8B4;
  }
        
      

        
          :root {
    --secondary-button-text-color: #3C3934;
  }
        
      

        
      

        
          :root {
    --link-color: #736D63;
  }
        
      

        
          :root {
    --title-font-color: #221F1B;
  }
        
      

        
          :root {
    --font-default-color: #736D63;
  }
        
      

        
          :root {
    --widget-background-color: #ffffff;
  }
        
      

        
          :root {
    --box-border-color: #BBB8B4;
  }
        
      

        
          :root {
    --font-light-color: #65676e;
  }
        
      

        
          :root {
    --input-text-color: #3C3934;
  }
        
      

        
          :root {
    --input-border-color: #BBB8B4;
    --border-default-color: #BBB8B4;
  }
        
      

        
          :root {
    --input-background-color: #ffffff;
  }
        
      

        
          :root {
    --icon-default-color: #3C3934;
  }
        
      

        
          :root {
    --error-color: #d03c38;
    --error-text-color: #ffffff;
  }
        
      

        
          :root {
    --success-color: #13a688;
  }
        
      

        
          :root {
    --base-focus-color: #000000;
    --transparency-focus-color: rgba(0,0,0, 0.15);
  }
        
      

        
          :root {
    --base-hover-color: #2f92db;
    --transparency-hover-color: rgba(47,146,219, var(--hover-transparency-value));
  }
        
      

        
      




        
          
        
      

        
          html, :root, .af-custom-form-container .af-form {
    font-size: 16px;
    --default-font-size: 16px;
  }
        
      

        
          body {
    --title-font-size: 1.5rem;
    --title-font-weight: var(--font-default-weight);
  }
        
      

        
          .ce52febd3 {
    font-size: 0.875rem;
    font-weight: var(--font-default-weight);
  }
        
      

        
          .c8663e6dc {
    font-size: 0.875rem;
    font-weight: var(--font-default-weight);
  }
  .ulp-passkey-benefit-heading {
    font-size: 1.025rem;
  }
        
      

        
          .ca768e8e3, .caed46cc2 {
    font-size: 1rem;
    font-weight: var(--font-default-weight);
  }
        
      

        
          body {
    --ulp-label-font-size: 1rem;
    --ulp-label-font-weight: var(--font-default-weight);
  }
        
      

        
          .c99f0490f, .c9e764a77, [id^='ulp-container-'] a {
    font-size: 0.875rem;
    font-weight: var(--font-bold-weight) !important;
  }
        
      

        
          
        
      




        
          :root {
    --button-border-width: 1px;
    --social-button-border-width: 1px;
    --radio-border-width: 1px;
  }
        
      

        
          body {
    --button-border-radius: 10px;
    --radio-border-radius: 10px;
  }
        
      

        
          :root {
    --input-border-width: 1px;
  }
        
      

        
          body {
    --input-border-radius: 10px;
  }

  .af-custom-form-container .af-form {
    --border-radius: 10px;
  }
        
      

        
          :root {
    --border-radius-outer: 30px;
  }
        
      

        
          :root {
    --box-border-width: 0px;
  }
        
      

        
          body {
    --shadow-component-outer: none;
  }
        
      




        
          
      .c8b615011 {
        display: none;
      }

      body {
        --header-title-spacing: 0;
      }
    
        
      

        
          
    .c8b615011 {
      content: url('https://www.augmentcode.com/android-chrome-512x512.png');
    }
  
        
      

        
          body {
    --logo-height: 52px;
  }
  .c8b615011 {
    height: var(--logo-height);
  }
  
        
      

        
          
    body {
      --header-alignment: center;
    }
  
        
      

        
          .ca8dfac6a {
    display: flex;
    flex-direction: column;
  }
  .ca8dfac6a form, .ca8dfac6a > .cda4f5e82 {
    margin: 0;
    order: 3;
  }
  .ca8dfac6a ._alternate-action, .ca8dfac6a #ulp-container-form-footer-start, .ca8dfac6a #ulp-container-form-footer-end {
    order: 4
  }
  .ca8dfac6a > .cbbdf7fe9 {
    order: 2;
    margin-bottom: 24px;
  }
  .ca8dfac6a .c72e08e4e {
    order: 1;
    margin-top: 0;
  }
  .ca8dfac6a > .c965efdef {
    margin-bottom: 12px;
  }
        
      




        
          .c4ebee5e9 {
    --page-background-alignment: center;
  }
        
      

        
          body {
    --page-background-color: #F5F5F4;
  }
        
      

        
          
        
      




      
    </style>
    <style>
    /* By default, hide features for javascript-disabled browsing */
    /* We use !important to override any css with higher specificity */
    /* It is also overriden by the styles in <noscript> in the header file */
    .no-js {
      clip: rect(0 0 0 0);
      clip-path: inset(50%);
      height: 1px;
      overflow: hidden;
      position: absolute;
      white-space: nowrap;
      width: 1px;
    }
  </style>
  <noscript>
    <style>
      /* We use !important to override the default for js enabled */
      /* If the display should be other than block, it should be defined specifically here */
      .js-required { display: none !important; }
      .no-js {
        clip: auto;
        clip-path: none;
        height: auto;
        overflow: auto;
        position: static;
        white-space: normal;
        width: 100%;
      }
      .no-js-container {
        width: var(--prompt-width);
      }
    </style>
  </noscript>
    

    <title>Sign in - Augment Code</title>
    

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://www.augmentcode.com/favicon.ico">

    <script type="text/javascript" crossorigin="anonymous" async="" src="https://us.i.posthog.com/static/array.js"></script><script src="https://connect.facebook.net/signals/config/4930228537203160?v=2.9.224&amp;r=stable&amp;domain=login.augmentcode.com&amp;hme=2e9ee56babe122798b967566f46100108daa710154b06378259c746cb66ac325&amp;ex_m=83%2C143%2C124%2C18%2C117%2C58%2C40%2C118%2C64%2C57%2C131%2C72%2C13%2C82%2C26%2C112%2C103%2C62%2C65%2C111%2C128%2C91%2C133%2C7%2C3%2C4%2C6%2C5%2C2%2C73%2C81%2C134%2C208%2C155%2C52%2C213%2C210%2C211%2C45%2C170%2C25%2C61%2C217%2C216%2C158%2C28%2C51%2C8%2C54%2C77%2C78%2C79%2C84%2C107%2C27%2C24%2C110%2C106%2C105%2C125%2C63%2C127%2C126%2C41%2C108%2C50%2C100%2C12%2C130%2C37%2C199%2C201%2C165%2C21%2C22%2C23%2C15%2C16%2C36%2C33%2C34%2C68%2C74%2C76%2C89%2C116%2C119%2C38%2C90%2C19%2C17%2C94%2C59%2C31%2C121%2C120%2C122%2C113%2C20%2C30%2C49%2C88%2C129%2C29%2C180%2C151%2C257%2C197%2C141%2C183%2C176%2C86%2C109%2C67%2C98%2C44%2C39%2C96%2C97%2C102%2C48%2C14%2C104%2C95%2C55%2C43%2C46%2C0%2C80%2C132%2C1%2C101%2C11%2C99%2C9%2C47%2C75%2C53%2C123%2C56%2C93%2C71%2C70%2C42%2C114%2C69%2C66%2C60%2C92%2C85%2C35%2C115%2C32%2C87%2C10%2C135" async=""></script><script type="text/javascript" async="" src="https://connect.facebook.net/en_US/fbevents.js"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/vendor/commons.59560acdd69ed701c941.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/twitter-ads/2.5.4/twitter-ads.dynamic.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/integrations/facebook-pixel/2.11.5/facebook-pixel.dynamic.js.gz" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/reddit-plugins/dc99c5c6506b994b53b9.js" async="" status="loaded"></script><script type="text/javascript" src="https://evs.grdt.augmentcode.com/next-integrations/actions/google-analytics-4-web/93409b67c1badd09287b.js" async="" status="loaded"></script><script type="text/javascript" async="" data-global-segment-analytics-key="analytics" src="https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js"></script><script>
      (function handleGlobalUTMs() {
        const utmParams = [
          "utm_source",
          "utm_medium",
          "utm_campaign",
          "utm_term",
          "utm_content",
        ];

        const utmData = (function (cookieName) {
          const cookies = document.cookie.split("; ");
          for (const cookie of cookies) {
            const [key, value] = cookie.split("=");
            if (key === cookieName) {
              try {
                return JSON.parse(decodeURIComponent(value));
              } catch (e) {
                console.error("Failed to parse cookie value:", e);
              }
            }
          }
          return null;
        })("cookieGlobalUTMs");

        if (utmData) {
          const currentUrl = new URL(window.location.href);

          utmParams.forEach((param) => {
            if (utmData[param] && !currentUrl.searchParams.has(param)) {
              currentUrl.searchParams.set(param, utmData[param]);
            }
          });

          if (currentUrl.toString() !== window.location.href) {
            window.history.replaceState(null, "", currentUrl.toString());
          }
        }
      })();
    </script>

    <!-- Segment and PostHog -->
    <script>
      // Load PostHog JS
      !(function (t, e) {
        var o, n, p, r;
        e.__SV ||
          ((window.posthog = e),
          (e._i = []),
          (e.init = function (i, s, a) {
            function g(t, e) {
              var o = e.split(".");
              2 == o.length && ((t = t[o[0]]), (e = o[1])),
                (t[e] = function () {
                  t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
                });
            }
            ((p = t.createElement("script")).type = "text/javascript"),
              (p.crossOrigin = "anonymous"),
              (p.async = !0),
              (p.src = s.api_host + "/static/array.js"),
              (r = t.getElementsByTagName("script")[0]).parentNode.insertBefore(
                p,
                r,
              );
            var u = e;
            for (
              void 0 !== a ? (u = e[a] = []) : (a = "posthog"),
                u.people = u.people || [],
                u.toString = function (t) {
                  var e = "posthog";
                  return (
                    "posthog" !== a && (e += "." + a), t || (e += " (stub)"), e
                  );
                },
                u.people.toString = function () {
                  return u.toString(1) + ".people (stub)";
                },
                o =
                  "capture identify alias people.set people.set_once set_config register register_once unregister opt_out_capturing has_opted_out_capturing opt_in_capturing reset isFeatureEnabled onFeatureFlags getFeatureFlag getFeatureFlagPayload reloadFeatureFlags group updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures getActiveMatchingSurveys getSurveys getNextSurveyStep".split(
                    " ",
                  ),
                n = 0;
              n < o.length;
              n++
            )
              g(u, o[n]);
            e._i.push([i, s, a]);
          }),
          (e.__SV = 1));
      })(document, window.posthog || []);

      !(function () {
        var i = "analytics",
          analytics = (window[i] = window[i] || []);
        if (!analytics.initialize)
          if (analytics.invoked)
            window.console &&
              console.error &&
              console.error("Segment snippet included twice.");
          else {
            analytics.invoked = !0;
            analytics.methods = [
              "trackSubmit",
              "trackClick",
              "trackLink",
              "trackForm",
              "pageview",
              "identify",
              "reset",
              "group",
              "track",
              "ready",
              "alias",
              "debug",
              "page",
              "screen",
              "once",
              "off",
              "on",
              "addSourceMiddleware",
              "addIntegrationMiddleware",
              "setAnonymousId",
              "addDestinationMiddleware",
              "register",
            ];
            analytics.factory = function (e) {
              return function () {
                if (window[i].initialized)
                  return window[i][e].apply(window[i], arguments);
                var n = Array.prototype.slice.call(arguments);
                if (
                  [
                    "track",
                    "screen",
                    "alias",
                    "group",
                    "page",
                    "identify",
                  ].indexOf(e) > -1
                ) {
                  var c = document.querySelector("link[rel='canonical']");
                  n.push({
                    __t: "bpc",
                    c: (c && c.getAttribute("href")) || void 0,
                    p: location.pathname,
                    u: location.href,
                    s: location.search,
                    t: document.title,
                    r: document.referrer,
                  });
                }
                n.unshift(e);
                analytics.push(n);
                return analytics;
              };
            };
            for (var n = 0; n < analytics.methods.length; n++) {
              var key = analytics.methods[n];
              analytics[key] = analytics.factory(key);
            }
            analytics.load = function (key, n) {
              var t = document.createElement("script");
              t.type = "text/javascript";
              t.async = !0;
              t.setAttribute("data-global-segment-analytics-key", i);
              t.src =
                "https://evs.grdt.augmentcode.com/puNFsgLRrSKXa3bQVZ5D2F/3TqKJBvcfQGExqhFkAzqai.min.js";
              var r = document.getElementsByTagName("script")[0];
              r.parentNode.insertBefore(t, r);
              analytics._loadOptions = n;
            };
            analytics._cdn = "https://evs.grdt.augmentcode.com";

            analytics._writeKey = "ki6UFHBqWCiflWcrR6BWiVUeEIVXLdkg";
            analytics.SNIPPET_VERSION = "5.2.0";
            analytics.load(analytics._writeKey);

            analytics.ready(() => {
              window.posthog.init(
                "phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW",
                {
                  api_host: "https://us.i.posthog.com",
                  segment: window.analytics,
                  capture_pageview: false,
                  capture_pageleave: true,

                  loaded: (posthog) => {
                    // When the posthog library has loaded, call `analytics.page()` explicitly.
                    
                    analytics.page("Login Page Visited");
                    
                  },
                },
              );
            });
          }
      })();
    </script>

    <!-- Google tag (gtag.js) -->
    <script async="" src="https://www.googletagmanager.com/gtag/js?id=G-F6GPDJDCJY"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      additionalParams = {};
      
      gtag("config", "G-F6GPDJDCJY", additionalParams);
    </script>

    <style type="text/css">
      *,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }/*! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com*/*,:after,:before{box-sizing:border-box;border:0 solid #e5e7eb}:after,:before{--tw-content:&#34;&#34;}:host,html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0}fieldset,legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}.mb-12{margin-bottom:3rem}.flex{display:flex}.hidden{display:none}.h-full{height:100%}.w-12{width:3rem}.w-64{width:16rem}.w-96{width:24rem}.w-\[20px\]{width:20px}.w-full{width:100%}.max-w-xl{max-width:36rem}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-center{justify-content:center}.gap-1{gap:.25rem}.gap-2{gap:.5rem}.gap-24{gap:6rem}.gap-4{gap:1rem}.gap-8{gap:2rem}.text-pretty{text-wrap:pretty}.rounded-xl{border-radius:.75rem}.bg-neutral-100{--tw-bg-opacity:1;background-color:rgb(245 245 245/var(--tw-bg-opacity,1))}.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity,1))}.text-center{text-align:center}.text-3xl{font-size:1.875rem;line-height:2.25rem}.text-sm{font-size:.875rem;line-height:1.25rem}.text-\[var\(--aug-green\)\]{color:var(--aug-green)}.text-\[var\(--aug-grey\)\]{color:var(--aug-grey)}.underline{text-decoration-line:underline}.underline-offset-2{text-underline-offset:2px}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}:root{--aug-grey:#736d63;--aug-green:#3d855e}header{padding:0!important;margin-bottom:.5rem}@media (min-width:1024px){.lg\:flex{display:flex}.lg\:w-1\/2{width:50%}.lg\:p-4{padding:1rem}}
    </style>
  <script type="text/javascript" async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/16732971011/?random=1755528919276&amp;cv=11&amp;fst=1755528919276&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=gtag.config&amp;gtm=45je58d1v9191852910za200zd9191852910xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527906~104528501~104684208~104684211~104948813~105033766~105033768~105103161~105103163~105231383~105231385&amp;u_w=1800&amp;u_h=900&amp;url=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fpasswordless-email-challenge%3Fstate%3DhKFo2SBIQTdsc2MzWWpBbzJUbTNKUHlxYkZXdjctdnFyVnNrdqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGZWWmJaNUZHeWNOS1NyTWNzMDFOTDd3S0JOWDFmLWtOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&amp;ref=https%3A%2F%2Flogin.augmentcode.com%2Fu%2Flogin%2Fidentifier%3Fstate%3DhKFo2SBIQTdsc2MzWWpBbzJUbTNKUHlxYkZXdjctdnFyVnNrdqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGZWWmJaNUZHeWNOS1NyTWNzMDFOTDd3S0JOWDFmLWtOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=Sign%20in%20-%20Augment%20Code&amp;npa=0&amp;pscdl=noapi&amp;auid=1339156309.1755528901&amp;data=event%3Dgtag.config&amp;rfmt=3&amp;fmt=4"></script></head>
  <body>
    <div class="flex h-full w-full gap-8 items-center justify-center bg-neutral-100 lg:p-4">
      
      <main class="flex flex-col lg:w-1/2 h-full items-center justify-center rounded-xl bg-white">
        <div class="flex flex-col text-center mb-12">
          <div class="w-64">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 233 32">
              <path fill="currentColor" fill-rule="evenodd" d="M211.967 2.78h2.887v23h-2.887v-2.569c-1.047 1.809-2.856 2.982-5.584 2.982-3.902 0-7.297-3.236-7.297-8.883 0-5.615 3.395-8.883 7.297-8.883 2.728 0 4.537 1.174 5.584 2.982V2.78Zm-4.854 8.122c-2.951 0-4.886 2.348-4.886 6.408 0 4.061 1.935 6.409 4.886 6.409 2.569 0 4.98-1.904 4.98-6.409s-2.411-6.408-4.98-6.408Zm12.722 7.297c.127 3.68 2.728 5.456 5.266 5.456s3.934-1.142 4.664-2.823h2.982c-.793 2.95-3.49 5.361-7.646 5.361-5.393 0-8.375-3.87-8.375-8.914 0-5.394 3.616-8.852 8.28-8.852 5.203 0 8.344 4.378 7.963 9.772h-13.134Zm.032-2.475h10.056c-.063-2.411-1.871-4.822-4.917-4.822-2.57 0-4.886 1.396-5.139 4.822Zm-29.986 10.47c3.966 0 8.185-2.697 8.185-8.884 0-6.186-4.219-8.883-8.185-8.883s-8.185 2.697-8.185 8.883c0 6.187 4.219 8.883 8.185 8.883Zm5.108-8.884c0 4.378-2.507 6.345-5.108 6.345-2.601 0-5.108-1.808-5.108-6.345 0-4.663 2.507-6.345 5.108-6.345 2.601 0 5.108 1.809 5.108 6.345Zm-14.277-2.887h-2.919c-.412-2.03-2.189-3.458-4.41-3.458-2.569 0-4.917 2-4.917 6.282 0 4.346 2.379 6.408 4.917 6.408 2.411 0 4.125-1.618 4.537-3.426h2.982c-.571 3.204-3.648 5.964-7.614 5.964-4.885 0-7.963-3.712-7.963-8.914 0-5.076 3.204-8.852 8.249-8.852 4.124 0 6.757 3.11 7.138 5.996ZM155.38 4.43h-2.887v4.283h-2.697v2.316h2.697v10.47c0 3.552.761 4.282 4.029 4.282h2.094V23.37h-1.46c-1.649 0-1.776-.444-1.776-2.22V11.028h3.236V8.713h-3.236V4.43Zm-17.659 6.853c.983-1.618 2.633-2.856 5.52-2.856 4.124 0 5.615 2.665 5.615 6.568V25.78h-2.887v-9.93c0-2.633-.444-4.917-3.743-4.917-2.792 0-4.505 1.935-4.505 5.583v9.264h-2.887V8.713h2.887v2.57Zm-12.613 12.372c-2.538 0-5.139-1.776-5.266-5.456h13.134c.381-5.394-2.76-9.772-7.963-9.772-4.663 0-8.28 3.458-8.28 8.852 0 5.044 2.982 8.914 8.375 8.914 4.156 0 6.853-2.41 7.646-5.361h-2.982c-.73 1.681-2.126 2.823-4.664 2.823Zm4.823-7.93h-10.057c.254-3.427 2.569-4.823 5.139-4.823 3.046 0 4.854 2.411 4.918 4.822ZM93.767 25.78H90.88V8.713h2.887v2.57c.983-1.618 2.57-2.856 5.14-2.856 2.601 0 4.155 1.143 4.917 3.014 1.364-2.094 3.362-3.014 5.742-3.014 3.965 0 5.52 2.665 5.52 6.568V25.78h-2.887v-9.93c0-2.633-.508-4.917-3.648-4.917-2.475 0-4.125 1.935-4.125 5.583v9.264h-2.887v-9.93c0-2.633-.507-4.917-3.648-4.917-2.475 0-4.124 1.935-4.124 5.583v9.264ZM87.829 8.713h-2.887v2.697c-1.047-1.809-2.697-2.983-5.425-2.983-3.776 0-7.234 3.078-7.234 8.534 0 5.489 3.458 8.534 7.234 8.534 2.728 0 4.378-1.142 5.425-2.95v1.618c0 2.316-.476 3.109-1.11 3.775-.762.825-1.936 1.27-3.49 1.27-2.665 0-3.585-1.175-3.966-2.697h-3.078c.54 3.458 3.141 5.234 7.012 5.234 2.538 0 4.663-.825 5.9-2.252.984-1.079 1.619-2.506 1.619-5.996V8.713ZM75.424 16.96c0-3.87 1.999-6.06 4.822-6.06 2.57 0 4.823 1.746 4.823 6.06 0 4.347-2.253 6.092-4.823 6.092-2.823 0-4.822-2.19-4.822-6.092Zm-7.927 6.378c-.983 1.618-2.538 2.855-5.361 2.855-3.966 0-5.457-2.665-5.457-6.567V8.713h2.887V18.77c0 2.634.444 4.918 3.585 4.918 2.728 0 4.346-1.936 4.346-5.584v-9.39h2.887v17.068h-2.887v-2.443Zm-16.246-8.06c-7.01.794-11.548 1.968-11.548 5.997 0 3.14 2.665 4.917 5.87 4.917 3.013 0 4.79-1.015 5.837-2.76.032 1.015.127 1.713.222 2.348h2.919c-.318-1.618-.476-3.585-.445-6.44l.032-3.934c.032-4.79-2.094-7.043-6.789-7.043-3.331 0-6.567 2.062-6.852 5.742h2.982c.127-2.094 1.523-3.395 3.902-3.395 2.125 0 3.87 1.047 3.87 4.156v.413Zm-8.343 5.933c0-2.347 3.33-3.109 8.565-3.648v1.079c0 4.029-2.57 5.266-5.266 5.266-2.062 0-3.3-1.079-3.3-2.697ZM14.316.185c.184-.101.433-.14.722-.14s.54.04.725.147a.593.593 0 0 1 .313.536v.008l-.159 4.22a.494.494 0 0 1-.278.446c-.157.083-.366.113-.601.113s-.444-.03-.601-.113a.494.494 0 0 1-.279-.447c-.04-1.05-.072-1.864-.098-2.441v-.005a47.416 47.416 0 0 0-.04-1.257c-.013-.248-.02-.406-.02-.465V.708c0-.108.025-.213.082-.307a.612.612 0 0 1 .234-.216Zm3.354 0c.184-.101.434-.14.722-.14.29 0 .54.04.725.147a.592.592 0 0 1 .313.536v.008l-.158 4.22a.494.494 0 0 1-.28.446c-.156.083-.365.113-.6.113s-.444-.03-.6-.113a.493.493 0 0 1-.279-.447 332.95 332.95 0 0 0-.099-2.441v-.005a46.976 46.976 0 0 0-.04-1.257c-.012-.248-.02-.406-.02-.465V.708c0-.108.026-.213.083-.307a.611.611 0 0 1 .233-.216Zm2.782 6.572c.2-.18.446-.264.727-.264h6.267c.805 0 1.452.215 1.91.672.46.458.673 1.115.673 1.933v5.332c0 .622.127 1.033.335 1.273.201.231.585.383 1.221.398h.01c.254.019.47.118.634.304a.99.99 0 0 1 .24.68c0 .25-.076.475-.233.67a.81.81 0 0 1-.653.313c-.634.015-1.018.167-1.22.398-.209.241-.334.656-.334 1.297v5.332c0 .541-.094 1.013-.293 1.41-.2.402-.498.703-.89.905h-.001c-.39.198-.86.292-1.399.292h-6.058v.005h-.21c-.284 0-.531-.092-.73-.28a.916.916 0 0 1-.301-.68c0-.251.084-.479.251-.668a.88.88 0 0 1 .686-.294h5.82c.296 0 .484-.075.603-.196.119-.119.198-.32.198-.653v-5.38c0-.5.1-.957.304-1.372.2-.413.474-.742.82-.985a1.97 1.97 0 0 1 .176-.11 1.994 1.994 0 0 1-.176-.11 2.518 2.518 0 0 1-.82-.987 3.082 3.082 0 0 1-.304-1.37V9.264c0-.33-.079-.533-.198-.653-.12-.12-.31-.197-.603-.197h-5.82a.886.886 0 0 1-.685-.294.976.976 0 0 1-.252-.669c0-.275.1-.512.305-.695Zm0 0v.001l.14.155-.14-.156ZM3.904 7.17c.457-.457 1.105-.672 1.91-.672h6.267c.282 0 .527.087.727.264v.001c.202.182.306.42.306.694 0 .255-.085.48-.252.668a.88.88 0 0 1-.686.295h-5.82c-.295 0-.483.077-.603.197-.118.118-.198.32-.198.653v5.357c0 .498-.1.956-.303 1.37a2.538 2.538 0 0 1-.82.986 1.99 1.99 0 0 1-.177.11c.06.034.12.07.176.11.346.242.62.573.82.987.203.414.304.872.304 1.37v5.38c0 .333.08.535.198.654.12.121.31.197.603.197h5.82c.272 0 .507.096.685.292.17.19.252.417.252.67a.909.909 0 0 1-.3.679c-.2.19-.448.28-.732.28H5.605v-.01c-.453-.022-.851-.115-1.19-.287a1.982 1.982 0 0 1-.89-.904c-.197-.396-.294-.87-.294-1.411v-5.332c0-.637-.127-1.055-.335-1.297-.2-.23-.584-.382-1.219-.397a.802.802 0 0 1-.653-.315 1.044 1.044 0 0 1-.233-.67.99.99 0 0 1 .24-.679.912.912 0 0 1 .633-.303l.01-.001c.637-.016 1.022-.167 1.222-.398.21-.241.335-.65.335-1.273V9.103c0-.818.215-1.475.673-1.933Zm18.622 7.617a2.276 2.276 0 1 0 0 4.552 2.276 2.276 0 0 0 0-4.552ZM8.939 17.063a2.276 2.276 0 1 1 4.552 0 2.276 2.276 0 0 1-4.552 0Z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div><main class="_widget login-passwordless-email-code">
  <section class="c0f89c152 _prompt-box-outer cf25e8e6e">
    <div class="c7fced0ba c4c9b8bb1">
      <div class="c8d0fff1c">
        <header class="c0e479d7d cad5295cb">
          <div title="augment" id="custom-prompt-logo" style="width: auto !important; height: 60px !important; position: static !important; margin: auto !important; padding: 0 !important; background-color: transparent !important; background-position: center !important; background-size: contain !important; background-repeat: no-repeat !important"></div>
        
          <img class="c8b615011 c833acd64" id="prompt-logo-center" src="https://www.augmentcode.com/android-chrome-512x512.png" alt="augment">
        
          
            <h1 class="cc6e7489a c3d00ba7d"> </h1>
          
        
          <div class="ce52febd3 ca45e2203">
            <p class="c6c99c202 cd6c8f199">We've sent an email with your <NAME_EMAIL></p>
          </div>
        </header>
      
        <div class="c8663e6dc ca8dfac6a">
          
        
          <div class="c9c6f39cb cda4f5e82">
            <div class="c9723764e">
              
                <form method="POST" class="c60907eb2 ca616590c" data-form-primary="true">
                  <input type="hidden" name="state" value="hKFo2SBIQTdsc2MzWWpBbzJUbTNKUHlxYkZXdjctdnFyVnNrdqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGZWWmJaNUZHeWNOS1NyTWNzMDFOTDd3S0JOWDFmLWtOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE">
                
                  
                
                  <div>
                    <div class="ce6f5cbba cad2e77da c1b2593df c7f6c0d8f">
                      <span class="ulp-authenticator-selector-text"><EMAIL></span>
                    
                      
                        <a class="c9e764a77 c5bd568d9 ce961c1fa cbe375faf" href="/u/login/identifier?state=hKFo2SBIQTdsc2MzWWpBbzJUbTNKUHlxYkZXdjctdnFyVnNrdqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGZWWmJaNUZHeWNOS1NyTWNzMDFOTDd3S0JOWDFmLWtOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE" aria-label="Edit email address">Edit</a>
                      
                    </div>
                  </div>
                
                  <div class="c9c6f39cb cda4f5e82">
                    <div class="c9723764e">
                      
                        <div class="input-wrapper _input-wrapper">
                          <div class="c95658709 c004ecf7a text c27eb450c c74bcfba6" data-action-text="" data-alternate-action-text="">
                            <label class="c964e0ce7 no-js c26b1455f ccfc91e63" for="code">
                              Enter the code
                            </label>
                          
                            <input class="input cd30b75df c1b3a3953" name="code" id="code" type="text" value="" required="" autocomplete="off" autocapitalize="none" spellcheck="false" autofocus="">
                          
                            <div class="c964e0ce7 js-required c26b1455f ccfc91e63" data-dynamic-label-for="code" aria-hidden="true">
                              Enter the code*
                            </div>
                          </div>
                        
                          
                        </div>
                      
                    </div>
                  </div>
                
                  
                
                  <div class="c15737573">
                    
                      <button type="submit" name="action" value="default" class="ca768e8e3 c85d593b5 cc25f8dff c0e04bc97 c07e6d7e4" data-action-button-primary="true">Continue</button>
                    
                  </div>
                </form>
              
            
              
            
              <div class="c3755a79d c965efdef">Didn't receive an email? 
                <form method="POST" class="c3467f493 ulp-action-form-resend">
                  <input type="hidden" name="state" value="hKFo2SBIQTdsc2MzWWpBbzJUbTNKUHlxYkZXdjctdnFyVnNrdqFur3VuaXZlcnNhbC1sb2dpbqN0aWTZIGZWWmJaNUZHeWNOS1NyTWNzMDFOTDd3S0JOWDFmLWtOo2NpZNkgd2xMVFZXR0RmSXRXOUh6aUlvd1NSaWVRTlJ5bE1QVGE">
                
                  <button type="submit" name="action" aria-label="" value="resend" class="c99f0490f _link-resend cbe375faf">Resend</button>
                </form>
              </div>
            
              
            </div>
          </div>
        </div>
      </div>
    </div>
  
    
  </section>
</main>
<script id="client-scripts">
window.ulpFlags = {"enable_ulp_wcag_compliance":false,"enable_ulp_rtl_support":false,"disable_ulp_form_submission_fix":false};!function(){var t,e,r,n={exports:function(t,e){return"object"==typeof t.ulpFlags&&null!==t.ulpFlags?t.ulpFlags:{}}}.exports(window,document),a=((t={}).exports=function(r,s){var n={},a={};function u(t,e){if(t.classList)return t.classList.add(e);var r=t.className.split(" ");-1===r.indexOf(e)&&(r.push(e),t.className=r.join(" "))}function i(t,e,r,n){return t.addEventListener(e,r,n)}function o(t){return"string"==typeof t}function l(t,e){return o(t)?s.querySelector(t):t.querySelector(e)}function c(t,e){if(t.classList)return t.classList.remove(e);var r=t.className.split(" "),n=r.indexOf(e);-1!==n&&(r.splice(n,1),t.className=r.join(" "))}function f(t,e){return t.getAttribute(e)}function d(t,e,r){return t.setAttribute(e,r)}function p(t){return t.remove()}var t=["text","number","email","password","tel","url"],e="select,textarea,"+t.map(function(t){return'input[type="'+t+'"]'}).join(",");return{addClass:u,toggleClass:function(t,e,r){if(!0===r||!1===r)return n=t,a=e,!0!==r?c(n,a):u(n,a);var n,a;if(t.classList)return t.classList.toggle(e);var i=t.className.split(" "),o=i.indexOf(e);-1!==o?i.splice(o,1):i.push(e),t.className=i.join(" ")},hasClass:function(t,e){return t.classList?t.classList.contains(e):-1!==t.className.split(" ").indexOf(e)},addClickListener:function(t,e){return i(t,"click",e)},addEventListener:i,getAttribute:f,hasAttribute:function(t,e){return t.hasAttribute(e)},getElementById:function(t){return s.getElementById(t)},getParent:function(t){return t.parentNode},isString:o,loadScript:function(t,e){var r=s.createElement("script");for(var n in e)n.startsWith("data-")?r.dataset[n.replace("data-","")]=e[n]:r[n]=e[n];r.src=t,s.body.appendChild(r)},removeScript:function(t){s.querySelectorAll('script[src="'+t+'"]').forEach(function(t){t.remove()})},poll:function(t){var i=t.interval||2e3,e=t.url||r.location.href,o=t.condition||function(){return!0},u=t.onSuccess||function(){},l=t.onError||function(){};return setTimeout(function n(){if(s.hidden)return setTimeout(n,i);var a=new XMLHttpRequest;return a.open("GET",e),a.setRequestHeader("Accept","application/json"),a.onload=function(){if(200===a.status){var t="application/json"===a.getResponseHeader("Content-Type").split(";")[0]?JSON.parse(a.responseText):a.responseText;return o(t)?u():setTimeout(n,i)}if(429!==a.status)return l({status:a.status,responseText:a.responseText});var e=1e3*Number.parseInt(a.getResponseHeader("X-RateLimit-Reset")),r=e-(new Date).getTime();return setTimeout(n,i<r?r:i)},a.send()},i)},querySelector:l,querySelectorAll:function(t,e){var r=o(t)?s.querySelectorAll(t):t.querySelectorAll(e);return Array.prototype.slice.call(r)},removeClass:c,removeElement:p,setAttribute:d,removeAttribute:function(t,e){return t.removeAttribute(e)},swapAttributes:function(t,e,r){var n=f(t,e),a=f(t,r);d(t,r,n),d(t,e,a)},setGlobalFlag:function(t,e){n[t]=!!e},getGlobalFlag:function(t){return!!n[t]},setSubmittedForm:function(t,e){a[t]=e},getSubmittedForm:function(t){return a[t]},preventFormSubmit:function(t){t.stopPropagation(),t.preventDefault()},matchMedia:function(t){return"function"!=typeof r.matchMedia&&r.matchMedia(t).matches},dispatchEvent:function(t,e,r){var n;"function"!=typeof Event?(n=s.createEvent("Event")).initCustomEvent(e,r,!1):n=new Event(e,{bubbles:r}),t.dispatchEvent(n)},timeoutPromise:function(t,a){return new Promise(function(e,r){var n=setTimeout(function(){r(new Error("timeoutPromise: promise timed out"))},t);a.then(function(t){clearTimeout(n),e(t)},function(t){clearTimeout(n),r(t)})})},createMutationObserver:function(t){return"undefined"==typeof MutationObserver?null:new MutationObserver(t)},consoleWarn:function(){(console.warn||console.log).apply(console,arguments)},getConfigJson:function(t){try{var e=l(t);if(!e)return null;var r=e.value;return r?JSON.parse(r):null}catch(t){return null}},getCSSVariable:function(t){return getComputedStyle(s.documentElement).getPropertyValue(t)},removeAndTrimString:function(t,e){var r=new RegExp(e,"g"),n=t.replace(r,"");return n=n.replace(/\s+/g,"  ").trim()},htmlEncode:function(t){var e=s.createTextNode(t),r=s.createElement("span");return r.appendChild(e),r.innerHTML||""},cleanServerErrorMessage:function(t,e){0<t.length&&0<e.length&&e.forEach(function(t){p(t)})},setTimeout:setTimeout,globalWindow:r,SUPPORTED_INPUT_TYPES:t,ELEMENT_TYPE_SELECTOR:e,RUN_INIT:!0}},t.exports)(window,document),i=function(){var t={};function m(t){if(!("string"==typeof t||t instanceof String)){var e=typeof t;throw null===t?e="null":"object"===e&&(e=t.constructor.name),new TypeError("Expected a string but received a "+e)}}function v(t,e){var r,n;m(t),n="object"==typeof e?(r=e.min||0,e.max):(r=e,arguments[2]);var a=encodeURI(t).split(/%..|./).length-1;return r<=a&&(void 0===n||a<=n)}function g(t,e){for(var r in void 0===t&&(t={}),e)void 0===t[r]&&(t[r]=e[r]);return t}var b={require_tld:!0,allow_underscores:!1,allow_trailing_dot:!1,allow_numeric_tld:!1,allow_wildcard:!1,ignore_max_length:!1},e="(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])",r="("+e+"[.]){3}"+e,n=new RegExp("^"+r+"$"),a="(?:[0-9a-fA-F]{1,4})",i=new RegExp("^((?:"+a+":){7}(?:"+a+"|:)|(?:"+a+":){6}(?:"+r+"|:"+a+"|:)|(?:"+a+":){5}(?::"+r+"|(:"+a+"){1,2}|:)|(?:"+a+":){4}(?:(:"+a+"){0,1}:"+r+"|(:"+a+"){1,3}|:)|(?:"+a+":){3}(?:(:"+a+"){0,2}:"+r+"|(:"+a+"){1,4}|:)|(?:"+a+":){2}(?:(:"+a+"){0,3}:"+r+"|(:"+a+"){1,5}|:)|(?:"+a+":){1}(?:(:"+a+"){0,4}:"+r+"|(:"+a+"){1,6}|:)|(?::((?::"+a+"){0,5}:"+r+"|(?::"+a+"){1,7}|:)))(%[0-9a-zA-Z-.:]{1,})?$");function h(t,e){return void 0===e&&(e=""),m(t),(e=String(e))?"4"===e?n.test(t):"6"===e&&i.test(t):h(t,4)||h(t,6)}var _={allow_display_name:!1,allow_underscores:!1,require_display_name:!1,allow_utf8_local_part:!0,require_tld:!0,blacklisted_chars:"",ignore_max_length:!1,host_blacklist:[],host_whitelist:[]},x=/^([^\x00-\x1F\x7F-\x9F\cX]+)</i,E=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~]+$/i,F=/^[a-z\d]+$/,w=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f]))*$/i,y=/^[a-z\d!#\$%&'\*\+\-\/=\?\^_`{\|}~\u00A1-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+$/i,S=/^([\s\x01-\x08\x0b\x0c\x0e-\x1f\x7f\x21\x23-\x5b\x5d-\x7e\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]|(\\[\x01-\x09\x0b\x0c\x0d-\x7f\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))*$/i,A=254;function o(t,e){if(m(t),(e=g(e,_)).require_display_name||e.allow_display_name){var r=t.match(x);if(r){var n=r[1];if(t=t.replace(n,"").replace(/(^<|>$)/g,""),n.endsWith(" ")&&(n=n.slice(0,-1)),!function(t){var e=t.replace(/^"(.+)"$/,"$1");if(!e.trim())return!1;if(/[\.";<>]/.test(e)){if(e===t)return!1;if(e.split('"').length!==e.split('\\"').length)return!1}return!0}(n))return!1}else if(e.require_display_name)return!1}if(!e.ignore_max_length&&t.length>A)return!1;var a=t.split("@"),i=a.pop(),o=i.toLowerCase();if(e.host_blacklist.includes(o))return!1;if(0<e.host_whitelist.length&&!e.host_whitelist.includes(o))return!1;var u=a.join("@");if(e.domain_specific_validation&&("gmail.com"===o||"googlemail.com"===o)){var l=(u=u.toLowerCase()).split("+")[0];if(!v(l.replace(/\./g,""),{min:6,max:30}))return!1;for(var s=l.split("."),c=0;c<s.length;c++)if(!F.test(s[c]))return!1}if(!(!1!==e.ignore_max_length||v(u,{max:64})&&v(i,{max:254})))return!1;if(!function(t,e){m(t),(e=g(e,b)).allow_trailing_dot&&"."===t[t.length-1]&&(t=t.substring(0,t.length-1)),!0===e.allow_wildcard&&0===t.indexOf("*.")&&(t=t.substring(2));var r=t.split("."),n=r[r.length-1];if(e.require_tld){if(r.length<2)return!1;if(!e.allow_numeric_tld&&!/^([a-z\u00A1-\u00A8\u00AA-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(n))return!1;if(/\s/.test(n))return!1}return!(!e.allow_numeric_tld&&/^\d+$/.test(n))&&r.every(function(t){return!(63<t.length&&!e.ignore_max_length||!/^[a-z_\u00a1-\uffff0-9-]+$/i.test(t)||/[\uff01-\uff5e]/.test(t)||/^-|-$/.test(t)||!e.allow_underscores&&/_/.test(t))})}(i,{require_tld:e.require_tld,ignore_max_length:e.ignore_max_length,allow_underscores:e.allow_underscores})){if(!e.allow_ip_domain)return!1;if(!h(i)){if(!i.startsWith("[")||!i.endsWith("]"))return!1;var f=i.slice(1,-1);if(0===f.length||!h(f))return!1}}if('"'===u[0])return u=u.slice(1,u.length-1),e.allow_utf8_local_part?S.test(u):w.test(u);for(var d=e.allow_utf8_local_part?y:E,p=(s=u.split("."),0);p<s.length;p++)if(!d.test(s[p]))return!1;return!e.blacklisted_chars||-1===u.search(new RegExp("["+e.blacklisted_chars+"]+","g"))}return t.exports=function(t,e){return{ulpRequiredFunction:function(t,e){return!e||!!t.value},ulpEmailValidationFunction:function(t,e){return!e||!t.value||!!o(t.value)},ulpPatternCheckFunction:function(t,e){return!e||!t.value||function(t){if("password"===t.name)return!0;var e=t.getAttribute("pattern");return!e||null!==t.value.match(e)}(t)}}},t.exports}()(window,document);((e={}).exports=function(n,t,a,i,o,u,l,s,r,c,e){if(!e.enable_ulp_wcag_compliance){if(n("body._simple-labels"))return t(".c964e0ce7.no-js").forEach(function(t){o(t,"no-js")}),void t(".c964e0ce7.js-required").forEach(function(t){i(t,"hide")});t(".c95658709:not(.c53dc55db):not(disabled)").forEach(function(t){i(t,"c74bcfba6");var e,r=n(t,".input");r.value&&i(t,"c773bd164"),a(t,"change",f),a(r,"blur",f),a(r,"animationstart",d),e=r,l(function(){e.value&&s(e,"change",!0)},100)})}function f(t){var e=t.target,r=u(e);e.value||c(e,"data-autofilled")?i(r,"c773bd164"):o(r,"c773bd164")}function d(t){var e=t.target;"onAutoFillStart"===t.animationName&&(r(e,"data-autofilled",!0),s(t.target,"change",!0),a(e,"keyup",p,{once:!0}))}function p(t){var e=t.target;r(e,"data-autofilled","")}},e.exports)(a.querySelector,a.querySelectorAll,a.addEventListener,a.addClass,a.removeClass,a.getParent,a.setTimeout,a.dispatchEvent,a.setAttribute,a.getAttribute,n),{exports:function(r,t,n,a,i,o,u,l,s,e,c,f){if(f.enable_ulp_wcag_compliance){var d=t("[id^='ulp-container-']");if(d&&d.length){var p=e(E);if(p)for(var m=0;m<d.length;m++)p.observe(d[m],{childList:!0,subtree:!0})}E()}function v(t){var e=t.target,r=o(e);e.value||l(e,"data-autofilled")?a(r,"c773bd164"):i(r,"c773bd164")}function g(t){var e=t.target,r=o(e);a(r,"focus"),x(e,r)}function b(t){var e=t.target,r=o(e);i(r,"focus"),v(t),x(e,r)}function h(t){var e=t.target;s(e,"data-autofilled","")}function _(t){var e=t.target;"onAutoFillStart"===t.animationName&&(s(e,"data-autofilled",!0),dispatchEvent(t.target,"change",!0),n(e,"keyup",h,{once:!0}))}function x(t,e){t.value?a(e,"c773bd164"):i(e,"c773bd164")}function E(){t(".ulp-field").forEach(function(t){if(!u(t,"c74bcfba6")){var e=r(t,c);e&&(a(t,"c74bcfba6"),x(e,t),setTimeout(function(){x(e,t)},50),e===document.activeElement&&a(t,"focus"),n(e,"change",v),n(e,"focus",g),n(e,"blur",b),n(e,"animationstart",_))}})}}}.exports(a.querySelector,a.querySelectorAll,a.addEventListener,a.addClass,a.removeClass,a.getParent,a.hasClass,a.getAttribute,a.setAttribute,a.createMutationObserver,a.ELEMENT_TYPE_SELECTOR,n),{exports:function(t,e,n,a,i,o,u,l){function r(t){var e=n("submitted"),r=i("submittedForm");a("submitted",!0),o("submittedForm",t.currentTarget),e&&r&&r===t.currentTarget?u(t):"apple"===l(t.target,"data-provider")&&setTimeout(function(){a("submitted",!1)},2e3)}var s=t("form");s&&s.forEach(function(t){e(t,"submit",r)})}}.exports(a.querySelectorAll,a.addEventListener,a.getGlobalFlag,a.setGlobalFlag,a.getSubmittedForm,a.setSubmittedForm,a.preventFormSubmit,a.getAttribute),{exports:function(n,o,a,i,u,l,s,c,f,d,p,m,e,v,g,t,r,b){if(b.enable_ulp_wcag_compliance){var h=!1,_=t+',input[type="checkbox"]';return T(),[_,x,E,F,w,y,S,A,T]}function x(t){var e=u(t,"data-ulp-validation-function"),r=i(t);return{functionName:e,element:n(r,_),parent:r}}function E(t){var a=[],i=[];return o(t,"[data-ulp-validation-function]").forEach(function(t){var e=x(t),r=[];if(e.element){if("input"===e.element.tagName.toLowerCase()){var n=u(e.element,"type");"checkbox"!==n&&-1===g.indexOf(n)&&r.push("Unsupported input type: "+n)}}else r.push("Could not find element");m[e.functionName]||r.push("Could not find function with name: "+e.functionName),r.length?i=i.concat(r):a.push(t)}),i.length&&e(i.join("\r\n")),a}function F(t,e,r){var n=x(t),a=(0,m[n.functionName])(n.element,e,r);a?c(t,"ulp-validator-error")&&(d(t,"ulp-validator-error"),s(t,"data-is-error")):c(t,"ulp-validator-error")||(f(t,"ulp-validator-error"),l(t,"data-is-error",!0));var i=o(n.parent,".ulp-validator-error");return p(n.parent,"ulp-error",!!i.length),a}function w(e){var r=x(e),t=(u(e,"data-ulp-validation-event-listeners")||"").replace(/\s/g,"").split(",").filter(function(t){return!!t});t.length&&t.forEach(function(t){a(r.element,t,function(){F(e,h,t)})})}function y(t,e,r){h=!0;var n=r.filter(function(t){return!F(t,h,"submit")});if(!n.length)return e.submitter&&"default"==u(e.submitter,"value")&&l(e.submitter,"disabled",!0),void t.submit();v(e);var a=x(n[0]);a.element.focus({preventScroll:!0}),a.parent.scrollIntoView({behavior:"smooth"})}function S(){var e=n('form[data-form-primary="true"]'),r=E(e);0!==r.length&&(r.forEach(function(t){w(t)}),a(e,"submit",function(t){y(e,t,r)}))}function A(){if(r)for(var t in r)r.hasOwnProperty(t)&&(m[t]=r[t])}function T(){var t=n("form[data-disable-html-validations]");t&&(A(),l(t,"novalidate",""),S())}}}.exports(a.querySelector,a.querySelectorAll,a.addEventListener,a.getParent,a.getAttribute,a.setAttribute,a.removeAttribute,a.hasClass,a.addClass,a.removeClass,a.toggleClass,a.globalWindow,a.consoleWarn,a.preventFormSubmit,a.SUPPORTED_INPUT_TYPES,a.ELEMENT_TYPE_SELECTOR,i,n),((r={}).exports=function(o,t,u,l,s,c,e,f,r){if(r.enable_ulp_wcag_compliance){var n=t('[class*="aria-error-check"]');if(n&&n.length){var a=e(function(t){t&&t.length&&t.map(function(t){if(t.target&&u(t.target,"aria-error-check")){var e=o('[id="'+l(t.target,"data-ulp-validation-target")+'"');if(e){var r=l(e,"aria-describedby");l(t.target,"data-is-error")?(n=e,a=r,i=t.target.id,a&&-1!==a.search(i)||s(n,"aria-describedby",a?a+" "+i:i),s(n,"aria-invalid",!0)):function(t,e,r){if(e){var n=f(e,r);n.length?s(t,"aria-describedby",n):(c(t,"aria-invalid"),c(t,"aria-describedby"))}else c(t,"aria-invalid"),c(t,"aria-describedby")}(e,r,t.target.id)}}var n,a,i})});a&&n.map(function(t){a.observe(t,{attributes:!0,attributeFilter:["class","data-is-error"]})})}}},r.exports)(a.querySelector,a.querySelectorAll,a.hasClass,a.getAttribute,a.setAttribute,a.removeAttribute,a.createMutationObserver,a.removeAndTrimString,n)}();
</script>
<div class="flex flex-col w-96 gap-8">
          <p class="text-[var(--aug-grey)] text-center">
            By signing up, you agree to the <br>
            
            <a href="https://www.augmentcode.com/terms-of-service/professional" class="text-[var(--aug-green)]" target="_blank">Terms of Service</a>
             and
            <a href="https://www.augmentcode.com/privacy-policy" class="text-[var(--aug-green)]" target="_blank">Privacy Policy</a>
          </p>
        </div>
      </main>
    </div><iframe height="0" width="0" style="display: none; visibility: hidden;"></iframe>
  

<script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/surveys.js?v=1.260.0"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/array/phc_Kc4h1nMQmkyKUo9uGYXOCt25GiiXwguFcnWr1Xhl6bW/config.js"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/web-vitals.js?v=1.260.0"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/dead-clicks-autocapture.js?v=1.260.0"></script><script type="text/javascript" crossorigin="anonymous" src="https://us-assets.i.posthog.com/static/recorder.js?v=1.260.0"></script></body></html>