import os
from dotenv import load_dotenv
from pathlib import Path

# Load .env file from parent directory - 强制重新加载
import os
env_path = Path(__file__).parent.parent / '.env'

# 清除可能的缓存环境变量
proxy_vars = ['PROXY_URL', 'PROXY_USER', 'PROXY_PASS', 'DRISSIONPAGE_PROXY', 'DRISSON_FIREFOX_PROXY']
for var in proxy_vars:
    if var in os.environ:
        del os.environ[var]

load_dotenv(env_path, override=True)  # 强制覆盖现有环境变量

class DrissionPageConfig:
    """
    DrissionPage Firefox Automation Configuration
    配置 DrissionPage Firefox 自动化的各种选项
    使用根目录的 .env 文件配置
    """

    def __init__(self):
        # Firefox 版本专用代理配置 - 优先使用 DRISSON_FIREFOX_PROXY
        firefox_proxy = os.getenv('DRISSON_FIREFOX_PROXY', 'false').lower() == 'true'
        fallback_proxy = os.getenv('DRISSIONPAGE_PROXY', 'false').lower() == 'true'

        # Firefox 版本使用专用配置，如果未设置则回退到通用配置
        self.drissionpage_proxy = firefox_proxy if os.getenv('DRISSON_FIREFOX_PROXY') else fallback_proxy
        self.drissionpage_recaptcha_solve = os.getenv('DRISSIONPAGE_RECAPTCHA_SOLVE', 'false').lower() == 'true'

        # 指纹防护配置 - Firefox 版本专用
        self.fingerprint_protection = os.getenv('DRISSON_FINGERPRINT_PROTECTION', 'true').lower() == 'true'

        # 浏览器显示模式配置 - Firefox 专用环境变量
        firefox_headfull = os.getenv('DRISSON_FIREFOX_HEADFULL', 'false').lower() == 'true'
        fallback_headfull = os.getenv('DRISSIONPAGE_HEADFULL', 'false').lower() == 'true'
        self.headfull = firefox_headfull if os.getenv('DRISSON_FIREFOX_HEADFULL') else fallback_headfull
        
        # 代理配置 (复用现有的代理配置)
        self.proxy_url = os.getenv('PROXY_URL')
        self.proxy_user = os.getenv('PROXY_USER')
        self.proxy_pass = os.getenv('PROXY_PASS')
        
        # YesCaptcha 配置
        self.yescaptcha_client_key = os.getenv('YESCAPTCHA_CLIENT_KEY')
        
        # 调试配置
        self.debug_mode = os.getenv('DEBUG_MODE', 'true').lower() == 'true'
        self.save_screenshots = os.getenv('SAVE_SCREENSHOTS', 'true').lower() == 'true'
        self.save_html = os.getenv('SAVE_HTML', 'true').lower() == 'true'
        
        # 超时配置
        self.page_timeout = int(os.getenv('PAGE_TIMEOUT', '30000'))
        self.email_check_timeout = int(os.getenv('EMAIL_CHECK_TIMEOUT', '120000'))
        self.email_check_interval = int(os.getenv('EMAIL_CHECK_INTERVAL', '5000'))
        
        # 验证配置
        self.validate()
    
    def validate(self):
        """验证配置"""
        if self.drissionpage_proxy and (not self.proxy_url or not self.proxy_user or not self.proxy_pass):
            print('⚠️ 警告: 启用了代理但代理配置不完整')
            print('💡 请在 .env 文件中设置: PROXY_URL, PROXY_USER, PROXY_PASS')
        
        if self.drissionpage_recaptcha_solve and not self.yescaptcha_client_key:
            print('⚠️ 警告: 启用了验证码解决但未设置 YesCaptcha 密钥')
            print('💡 请在 .env 文件中设置: YESCAPTCHA_CLIENT_KEY')
    
    def get_proxy_config(self):
        """获取代理配置（强制重新读取 .env 确保最新值）"""
        if not self.drissionpage_proxy or not self.proxy_url:
            return None

        # 强制重新读取 .env 文件，确保获取最新的用户名和密码
        load_dotenv(env_path, override=True)
        current_proxy_user = os.getenv('PROXY_USER', '')
        current_proxy_pass = os.getenv('PROXY_PASS', '')

        # 处理带协议前缀的代理 URL (如 socks5://host:port)
        proxy_url = self.proxy_url
        proxy_type = 'http'  # 默认类型

        if proxy_url.startswith('socks5://'):
            proxy_type = 'socks5'
            proxy_url = proxy_url[9:]  # 移除 socks5:// 前缀
        elif proxy_url.startswith('http://'):
            proxy_type = 'http'
            proxy_url = proxy_url[7:]  # 移除 http:// 前缀
        elif proxy_url.startswith('https://'):
            proxy_type = 'https'
            proxy_url = proxy_url[8:]  # 移除 https:// 前缀

        host, port = proxy_url.split(':')
        return {
            'host': host,
            'port': int(port),
            'username': current_proxy_user,
            'password': current_proxy_pass,
            'type': proxy_type
        }
    
    def print_config(self):
        """打印配置信息"""
        print('🔧 DrissionPage Firefox 配置:')

        # 显示浏览器显示模式配置来源
        firefox_headfull_set = os.getenv('DRISSON_FIREFOX_HEADFULL') is not None
        headfull_source = "DRISSON_FIREFOX_HEADFULL" if firefox_headfull_set else "DRISSIONPAGE_HEADFULL (fallback)"
        print(f'   🦊 Firefox 显示: {"是" if self.headfull else "否 (headless模式)"} (来源: {headfull_source})')

        # 显示代理配置来源
        firefox_proxy_set = os.getenv('DRISSON_FIREFOX_PROXY') is not None
        proxy_source = "DRISSON_FIREFOX_PROXY" if firefox_proxy_set else "DRISSIONPAGE_PROXY (fallback)"
        print(f'   📡 代理: {"启用" if self.drissionpage_proxy else "禁用"} (来源: {proxy_source})')

        if self.drissionpage_proxy and self.proxy_url:
            print(f'   🌐 代理地址: {self.proxy_url}')
            print(f'   👤 代理用户: {self.proxy_user}')
        print(f'   🤖 验证码解决: {"启用" if self.drissionpage_recaptcha_solve else "禁用"}')
        print(f'   🛡️ 指纹防护: {"启用" if self.fingerprint_protection else "禁用"}')
        print(f'   📸 截图保存: {"启用" if self.save_screenshots else "禁用"}')
        print(f'   📄 HTML保存: {"启用" if self.save_html else "禁用"}')
        print(f'   ⏱️ 页面超时: {self.page_timeout}ms')
        print(f'   📧 邮箱检查超时: {self.email_check_timeout}ms')
