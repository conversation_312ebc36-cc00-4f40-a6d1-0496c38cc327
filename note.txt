mkdir -p ~/.config/systemd/user
nano ~/.config/systemd/user/xhost-johnson.service


[Unit]
Description=Allow johnson to use X11
After=graphical-session.target

[Service]
Type=oneshot
ExecStart=/usr/bin/xhost +SI:localuser:johnson
Environment=DISPLAY=:2
RemainAfterExit=yes

[Install]
WantedBy=graphical-session.target

systemctl --user enable --now xhost-johnson.service




https://github.com/adryfish/fingerprint-chromium/releases/download/136.0.7103.113/ungoogled-chromium_136.0.7103.113-1_linux.tar.xzs


--------------

# 最简单但安全性较低的方案：全局开放X11

# 在pc用户的桌面终端创建开机自启动
mkdir -p ~/.config/autostart
cat > ~/.config/autostart/xhost-open.desktop << 'EOF'
[Desktop Entry]
Type=Application
Exec=xhost +
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
Name=Open X11 Access
Comment=Allow all users to access X11 display
StartupNotify=false
EOF

chmod +x ~/.config/autostart/xhost-open.desktop

# 这样所有用户都能直接使用图形程序，包括johnson
echo "全局X11访问已开放，johnson用户可以直接运行chrome"