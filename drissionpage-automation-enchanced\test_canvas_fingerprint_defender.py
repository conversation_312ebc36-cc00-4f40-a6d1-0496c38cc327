#!/usr/bin/env python3
"""
Canvas Fingerprint Defender 插件测试脚本
测试 Canvas Fingerprint Defender 插件的集成和效果
"""

import sys
import os
import time
from pathlib import Path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from drissionpage_automation import DrissionPageAutomation
from fingerprint_defender_plugin import FingerprintDefenderPlugin, download_canvas_fingerprint_defender

def check_plugin_availability():
    """检查 Canvas Fingerprint Defender 插件是否可用"""
    print("🔍 检查 Canvas Fingerprint Defender 插件可用性")
    print("=" * 60)
    
    base_path = Path(__file__).parent
    
    # 检查 .crx 文件
    crx_path = base_path / 'canvas_fingerprint_defender.crx'
    if crx_path.exists():
        print(f"✅ 找到 CRX 文件: {crx_path}")
        print(f"   文件大小: {crx_path.stat().st_size} 字节")
        return True, str(crx_path)
    
    # 检查文件夹
    folder_path = base_path / 'canvas_fingerprint_defender'
    if folder_path.exists():
        manifest_path = folder_path / 'manifest.json'
        if manifest_path.exists():
            print(f"✅ 找到插件文件夹: {folder_path}")
            try:
                import json
                with open(manifest_path, 'r', encoding='utf-8') as f:
                    manifest = json.load(f)
                print(f"   插件名称: {manifest.get('name', 'Unknown')}")
                print(f"   插件版本: {manifest.get('version', 'Unknown')}")
                return True, str(folder_path)
            except Exception as e:
                print(f"⚠️ 读取 manifest.json 失败: {e}")
        else:
            print(f"❌ 文件夹存在但缺少 manifest.json: {folder_path}")
    
    print("❌ 未找到 Canvas Fingerprint Defender 插件")
    print("💡 请参考下载指南获取插件")
    return False, None

def test_plugin_integration():
    """测试插件集成"""
    print("\n🧪 测试 Canvas Fingerprint Defender 插件集成")
    print("=" * 60)
    
    automation = None
    try:
        # 检查插件可用性
        plugin_available, plugin_path = check_plugin_availability()
        
        # 初始化自动化实例
        print("\n🚀 初始化 DrissionPage 自动化...")
        automation = DrissionPageAutomation()
        
        # 检查配置
        if automation.config.fingerprint_protection:
            print("✅ 指纹防护已启用")
        else:
            print("⚠️ 指纹防护已禁用，请设置 DRISSON_FINGERPRINT_PROTECTION=true")
            return False
        
        # 启动浏览器
        print("🌐 启动浏览器...")
        automation.init_browser()
        print("✅ 浏览器启动成功")
        
        # 检查扩展是否加载
        print("\n🔍 检查浏览器扩展...")
        extensions_info = automation.page.run_js("""
            return {
                extensionsAvailable: !!chrome.runtime,
                userAgent: navigator.userAgent,
                webdriver: navigator.webdriver,
                plugins: navigator.plugins.length
            };
        """)
        
        print(f"   Chrome Runtime: {'✅ 可用' if extensions_info['extensionsAvailable'] else '❌ 不可用'}")
        print(f"   WebDriver 属性: {extensions_info['webdriver']}")
        print(f"   插件数量: {extensions_info['plugins']}")
        
        # 测试 Canvas 指纹防护
        print("\n🎨 测试 Canvas 指纹防护...")
        canvas_test_results = test_canvas_fingerprint_protection(automation)
        
        # 测试页面导航
        print("\n📄 测试页面导航...")
        test_url = "https://browserleaks.com/canvas"
        automation.navigate_to_page(test_url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 检查页面标题
        try:
            title = automation.page.title
            print(f"✅ 页面加载成功: {title}")
        except Exception as e:
            print(f"⚠️ 获取页面标题失败: {e}")
        
        print("\n✅ 插件集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        if automation and automation.page:
            print("\n🧹 清理资源...")
            automation.cleanup()

def test_canvas_fingerprint_protection(automation):
    """测试 Canvas 指纹防护效果"""
    print("🎨 测试 Canvas 指纹防护效果...")
    
    try:
        # 创建多个 Canvas 指纹并比较
        fingerprints = []
        
        for i in range(3):
            fingerprint = automation.page.run_js("""
                // 创建 Canvas 元素
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // 绘制测试图案
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillStyle = '#f60';
                ctx.fillRect(125, 1, 62, 20);
                ctx.fillStyle = '#069';
                ctx.fillText('Canvas Fingerprint Test 🛡️', 2, 15);
                ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
                ctx.fillText('Canvas Fingerprint Test 🛡️', 4, 17);
                
                // 获取指纹
                return canvas.toDataURL();
            """)
            
            fingerprints.append(fingerprint[:100] + '...')  # 只保留前100个字符用于比较
            print(f"   指纹 {i+1}: {fingerprints[i]}")
            
            # 短暂延迟
            time.sleep(0.5)
        
        # 分析结果
        unique_fingerprints = len(set(fingerprints))
        if unique_fingerprints > 1:
            print(f"✅ Canvas 指纹防护有效: 生成了 {unique_fingerprints} 个不同的指纹")
        else:
            print(f"⚠️ Canvas 指纹防护可能无效: 所有指纹相同")
        
        return {
            'fingerprints': fingerprints,
            'unique_count': unique_fingerprints,
            'protection_effective': unique_fingerprints > 1
        }
        
    except Exception as e:
        print(f"❌ Canvas 指纹测试失败: {e}")
        return None

def show_plugin_download_guide():
    """显示插件下载指南"""
    print("\n📋 Canvas Fingerprint Defender 插件下载指南")
    print("=" * 60)
    download_canvas_fingerprint_defender()

def main():
    """主测试函数"""
    print("🧪 Canvas Fingerprint Defender 插件测试套件")
    print("=" * 70)
    
    # 检查插件可用性
    plugin_available, plugin_path = check_plugin_availability()
    
    if not plugin_available:
        print("\n⚠️ 插件不可用，显示下载指南...")
        show_plugin_download_guide()
        print("\n💡 获取插件后重新运行此测试")
        return False
    
    # 运行集成测试
    integration_success = test_plugin_integration()
    
    # 总结
    print(f"\n🎯 测试总结")
    print("=" * 70)
    print(f"插件可用性: {'✅ 可用' if plugin_available else '❌ 不可用'}")
    print(f"集成测试: {'✅ 通过' if integration_success else '❌ 失败'}")
    
    if plugin_available and integration_success:
        print("\n🎉 Canvas Fingerprint Defender 插件集成成功！")
        print("\n💡 功能说明:")
        print("   - 自动防护 Canvas 指纹识别")
        print("   - 每次生成不同的 Canvas 指纹")
        print("   - 与 DrissionPage 自动化完美集成")
        print("   - 提高自动化脚本的隐蔽性")
        return True
    else:
        print("\n⚠️ 插件集成存在问题，请检查:")
        print("   1. 插件文件是否正确放置")
        print("   2. 插件文件是否完整")
        print("   3. 浏览器是否支持该插件")
        print("   4. 配置是否正确")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
