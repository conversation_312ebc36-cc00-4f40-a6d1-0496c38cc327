# DrissionPage Automation

基于 DrissionPage 的 Python 自动化脚本，用于执行邮箱验证流程。这是 `real-browser-automation` 的 Python 版本，具有完全相同的功能和逻辑。

## 功能特性

- 🐍 **Python 实现**: 使用 DrissionPage 库进行浏览器自动化
- 🔄 **完全兼容**: 与 `real-browser-automation` 逻辑完全一致
- 🤖 **验证码处理**: 支持 Turnstile 和 reCAPTCHA 自动解决
- 📧 **邮箱验证**: 自动生成临时邮箱并获取验证码
- 🌐 **代理支持**: 支持 HTTP/SOCKS5 代理配置
- 📸 **调试功能**: 自动保存截图和 HTML 文件
- 📋 **日志记录**: 详细的操作日志和错误追踪

## 安装依赖

```bash
cd drissionpage-automation
pip install -r requirements.txt
```

## 配置

在根目录的 `.env` 文件中添加以下配置：

```env
# DrissionPage 配置
DRISSIONPAGE_PROXY=false
DRISSIONPAGE_RECAPTCHA_SOLVE=true

# 代理配置 (如果启用代理)
PROXY_URL=your_proxy_host:port
PROXY_USER=your_proxy_username
PROXY_PASS=your_proxy_password

# YesCaptcha 配置
YESCAPTCHA_CLIENT_KEY=your_yescaptcha_key

# 调试配置
DEBUG_MODE=true
SAVE_SCREENSHOTS=true
SAVE_HTML=true

# 超时配置
PAGE_TIMEOUT=30000
EMAIL_CHECK_TIMEOUT=120000
EMAIL_CHECK_INTERVAL=5000
```

## 使用方法

### 运行邮箱验证流程

```bash
cd drissionpage-automation
python run_drissionpage_verification.py
```

### 流程步骤

1. **生成授权URL**: 创建 Augment OAuth 授权链接
2. **生成临时邮箱**: 使用 OneMail API 生成临时邮箱
3. **启动浏览器**: 初始化 DrissionPage 浏览器
4. **导航到页面**: 访问授权页面
5. **输入邮箱**: 填入临时邮箱地址
6. **处理验证码**: 自动解决 Turnstile 验证码
7. **点击继续**: 提交邮箱表单
8. **等待验证页面**: 等待验证码输入页面
9. **获取验证码**: 从邮箱获取验证码
10. **输入验证码**: 填入验证码
11. **点击继续**: 提交验证码
12. **等待授权页面**: 等待授权码页面
13. **提取授权码**: 获取授权码数据
14. **完成OAuth**: 调用 Augment API 获取令牌
15. **保存令牌**: 将令牌保存到 tokens.json

## 文件结构

```
drissionpage-automation/
├── config.py                          # 配置管理
├── drissionpage_logger.py             # 日志记录器
├── drissionpage_automation.py         # 主自动化类
├── run_drissionpage_verification.py   # 运行脚本
├── requirements.txt                   # Python 依赖
├── README.md                          # 说明文档
├── logs/                              # 日志文件目录
├── screenshots/                       # 截图文件目录
└── html/                              # HTML 文件目录
```

## 与 real-browser-automation 的对比

| 特性 | real-browser-automation | drissionpage-automation |
|------|------------------------|------------------------|
| 语言 | JavaScript (Node.js) | Python |
| 浏览器库 | puppeteer-real-browser | DrissionPage |
| 功能 | ✅ 完整功能 | ✅ 完整功能 |
| 验证码处理 | ✅ Turnstile + reCAPTCHA | ✅ Turnstile + reCAPTCHA |
| 代理支持 | ✅ HTTP/SOCKS5 | ✅ HTTP/SOCKS5 |
| 调试功能 | ✅ 截图 + HTML + 日志 | ✅ 截图 + HTML + 日志 |
| 成功率 | 🔥 Windows 下非常高 | 🔥 跨平台高成功率 |

## 调试信息

运行后会生成以下调试文件：

- **日志文件**: `logs/drissionpage-YYYY-MM-DD.log`
- **截图文件**: `screenshots/STEP_XX_*.png`
- **HTML文件**: `html/STEP_XX_*.html`
- **令牌文件**: `../tokens.json`

## 错误处理

如果遇到问题，请检查：

1. **依赖安装**: 确保所有 Python 包已正确安装
2. **配置文件**: 检查 `.env` 文件中的配置
3. **网络连接**: 确保网络连接正常
4. **代理设置**: 如果使用代理，确保代理配置正确
5. **调试文件**: 查看截图和 HTML 文件了解失败原因

## 注意事项

- 确保 Chrome/Chromium 浏览器已安装
- 如果使用代理，确保代理服务器可用
- YesCaptcha 密钥需要有足够余额
- 建议在稳定的网络环境下运行

## 技术细节

### 验证码处理策略

1. **检测验证码**: 自动检测页面中的 Turnstile 和 reCAPTCHA
2. **获取 siteKey**: 提取验证码的 siteKey 参数
3. **调用 YesCaptcha**: 使用 YesCaptcha API 解决验证码
4. **注入 token**: 将获得的 token 注入到页面表单中
5. **同步字段**: 确保 token 在正确的表单字段中

### 授权码提取策略

1. **拦截剪贴板**: 劫持 `clipboard.writeText` 获取复制内容
2. **点击复制按钮**: 模拟点击复制按钮并读取剪贴板
3. **JavaScript 提取**: 从页面脚本中提取授权码数据
4. **URL 提取**: 从页面 URL 中提取授权码参数
5. **正则匹配**: 使用正则表达式从页面内容中提取

这些策略确保了在各种页面结构下都能成功提取授权码。
