"""
GUI模式配置 - 如果需要看到浏览器界面
在Ubuntu环境下启用GUI模式的特殊配置
"""

def configure_gui_mode_options(options):
    """配置GUI模式的Chrome选项"""
    
    # 基础安全选项
    options.set_argument('--no-sandbox')
    options.set_argument('--disable-setuid-sandbox')
    options.set_argument('--disable-dev-shm-usage')
    options.set_argument('--disable-gpu')
    options.set_argument('--no-first-run')
    
    # Ubuntu GUI模式特殊配置
    options.set_argument('--disable-web-security')
    options.set_argument('--disable-features=VizDisplayCompositor')
    options.set_argument('--disable-ipc-flooding-protection')
    options.set_argument('--remote-debugging-port=9222')
    options.set_argument('--user-data-dir=/tmp/chrome-drissionpage')
    
    # 显示相关配置
    options.set_argument('--start-maximized')
    options.set_argument('--disable-infobars')
    options.set_argument('--disable-extensions')
    
    return options

# 使用方法：
# from config_gui_mode import configure_gui_mode_options
# options = ChromiumOptions()
# options = configure_gui_mode_options(options)
