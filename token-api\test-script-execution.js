#!/usr/bin/env node

/**
 * Simple test to verify Python automation script can be called
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:9043';
const AUTH_TOKEN = 'your_secret_password_here_change_this';

async function testScriptExecution() {
  console.log('🧪 Testing Python automation script execution...\n');

  try {
    console.log('Triggering 1 automation script...');
    const response = await axios.post(`${BASE_URL}/api/tokens/trigger-automation`, 
      { count: 1 }, 
      { 
        headers: { 
          'Authorization': `Bearer ${AUTH_TOKEN}`, 
          'Content-Type': 'application/json' 
        } 
      }
    );
    
    console.log(`✅ ${response.data.message}`);
    console.log('\n📝 Check server console for script execution details');
    console.log('   Script will run for up to 5 minutes with timeout');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testScriptExecution();
