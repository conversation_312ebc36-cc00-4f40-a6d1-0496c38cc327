# Token API Usage Examples

## Table of Contents
- [JavaScript/Node.js Examples](#javascriptnodejs-examples)
- [Python Examples](#python-examples)
- [cURL Examples](#curl-examples)
- [Integration Patterns](#integration-patterns)

---

## JavaScript/Node.js Examples

### Basic Setup
```javascript
const axios = require('axios');

const API_BASE_URL = 'http://localhost:9043';
const AUTH_TOKEN = 'your_secret_password_here_change_this';

const headers = {
  'Authorization': `Bearer ${AUTH_TOKEN}`,
  'Content-Type': 'application/json'
};
```

### Get Valid Token Count
```javascript
async function getValidTokenCount() {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/tokens/valid-count`, { headers });
    console.log('Valid tokens:', response.data.validCount);
    return response.data.validCount;
  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}
```

### Save New Token
```javascript
async function saveToken(accessToken, tenantUrl, emailNote = null) {
  const tokenData = {
    access_token: accessToken,
    tenant_url: tenantUrl,
    email_note: emailNote,
    description: 'Token from automation script',
    user_agent: 'my-automation-script',
    session_id: `session_${Date.now()}`,
    created_timestamp: Date.now()
  };

  try {
    const response = await axios.post(`${API_BASE_URL}/api/tokens/save`, tokenData, { headers });
    console.log('Token saved:', response.data.tokenId);
    return response.data;
  } catch (error) {
    console.error('Error saving token:', error.response?.data || error.message);
  }
}
```

### Get Unused Token
```javascript
async function getUnusedToken() {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/tokens`, { headers });
    const token = response.data.token;
    console.log('Got token:', {
      id: token.id,
      tenantURL: token.tenantURL
    });
    return token;
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('No available tokens');
    } else {
      console.error('Error:', error.response?.data || error.message);
    }
  }
}
```

---

## Python Examples

### Basic Setup
```python
import requests
import json
import time

API_BASE_URL = 'http://localhost:9043'
AUTH_TOKEN = 'your_secret_password_here_change_this'

headers = {
    'Authorization': f'Bearer {AUTH_TOKEN}',
    'Content-Type': 'application/json'
}
```

### Token API Client Class
```python
class TokenAPIClient:
    def __init__(self, base_url='http://localhost:9043', auth_token=None):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }
    
    def get_valid_count(self):
        """Get count of valid tokens"""
        try:
            response = requests.get(f'{self.base_url}/api/tokens/valid-count', headers=self.headers)
            response.raise_for_status()
            return response.json()['validCount']
        except requests.exceptions.RequestException as e:
            print(f'Error getting valid count: {e}')
            return 0
    
    def save_token(self, access_token, tenant_url, **kwargs):
        """Save a new token"""
        data = {
            'access_token': access_token,
            'tenant_url': tenant_url,
            'created_timestamp': int(time.time() * 1000),
            **kwargs
        }
        
        try:
            response = requests.post(f'{self.base_url}/api/tokens/save', headers=self.headers, json=data)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f'Error saving token: {e}')
            return {'success': False, 'error': str(e)}
    
    def get_unused_token(self):
        """Get an unused token"""
        try:
            response = requests.get(f'{self.base_url}/api/tokens', headers=self.headers)
            response.raise_for_status()
            return response.json()['token']
        except requests.exceptions.RequestException as e:
            if hasattr(e, 'response') and e.response.status_code == 404:
                print('No available tokens')
            else:
                print(f'Error getting token: {e}')
            return None
    
    def get_stats(self):
        """Get token statistics"""
        try:
            response = requests.get(f'{self.base_url}/api/tokens/stats', headers=self.headers)
            response.raise_for_status()
            return response.json()['stats']
        except requests.exceptions.RequestException as e:
            print(f'Error getting stats: {e}')
            return None
    
    def trigger_automation(self, count=1):
        """Trigger automation script"""
        try:
            response = requests.post(
                f'{self.base_url}/api/tokens/trigger-automation',
                headers=self.headers,
                json={'count': count}
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f'Error triggering automation: {e}')
            return {'success': False, 'error': str(e)}
```

### Usage Example
```python
# Initialize client
client = TokenAPIClient(auth_token='your_secret_password_here_change_this')

# Check current status
valid_count = client.get_valid_count()
print(f'Valid tokens: {valid_count}')

# Save a new token
result = client.save_token(
    access_token='example_token_123',
    tenant_url='https://d1.api.augmentcode.com/',
    description='Token from Python script',
    email_note='<EMAIL>'
)

if result['success']:
    print(f'Token saved: {result["tokenId"]}')

# Get an unused token
token = client.get_unused_token()
if token:
    print(f'Using token: {token["accessToken"][:20]}...')
```

---

## cURL Examples

### Health Check
```bash
curl -X GET http://localhost:9043/health
```

### Get Valid Token Count
```bash
curl -X GET \
  http://localhost:9043/api/tokens/valid-count \
  -H "Authorization: Bearer your_secret_password_here_change_this"
```

### Save New Token
```bash
curl -X POST \
  http://localhost:9043/api/tokens/save \
  -H "Authorization: Bearer your_secret_password_here_change_this" \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "example_token_123",
    "tenant_url": "https://d1.api.augmentcode.com/",
    "description": "Token from cURL",
    "email_note": "<EMAIL>"
  }'
```

### Get Unused Token
```bash
curl -X GET \
  http://localhost:9043/api/tokens \
  -H "Authorization: Bearer your_secret_password_here_change_this"
```

### Get Token Statistics
```bash
curl -X GET \
  http://localhost:9043/api/tokens/stats \
  -H "Authorization: Bearer your_secret_password_here_change_this"
```

### Trigger Automation
```bash
curl -X POST \
  http://localhost:9043/api/tokens/trigger-automation \
  -H "Authorization: Bearer your_secret_password_here_change_this" \
  -H "Content-Type: application/json" \
  -d '{"count": 2}'
```

---

## Integration Patterns

### For DrissionPage Automation Scripts

Replace your token saving logic:

```python
# OLD: Direct file writing
# tokens.append(new_token)
# save_tokens_to_file(tokens)

# NEW: API integration
def save_token_via_api(access_token, tenant_url, email_note):
    client = TokenAPIClient(auth_token='your_secret_password_here_change_this')
    result = client.save_token(
        access_token=access_token,
        tenant_url=tenant_url,
        email_note=email_note,
        description='Firefox token from fixed automation',
        user_agent='firefox-fixed-automation',
        session_id=f'firefox_fixed_{int(time.time())}'
    )
    
    if result['success']:
        print(f'✅ Token saved to API: {result["tokenId"]}')
        # Check remaining valid tokens
        valid_count = client.get_valid_count()
        print(f'📊 Valid tokens remaining: {valid_count}')
    else:
        print(f'❌ Failed to save token: {result.get("message")}')
        # Fallback to file saving if needed
        fallback_save_to_file(access_token, tenant_url, email_note)
```

### For Real Browser Automation (Node.js)

```javascript
// Integration in real-browser-automation.js
async function saveTokenViaAPI(accessToken, tenantUrl, emailNote) {
  const tokenData = {
    access_token: accessToken,
    tenant_url: tenantUrl,
    email_note: emailNote,
    description: 'Real Browser token from Augment API via email verification',
    user_agent: 'real-browser-email-verification',
    session_id: `real_browser_session_${Date.now()}`
  };

  try {
    const response = await axios.post(
      'http://localhost:9043/api/tokens/save',
      tokenData,
      { headers }
    );
    
    console.log(`✅ Token saved to API: ${response.data.tokenId}`);
    
    // Check remaining tokens
    const countResponse = await axios.get(
      'http://localhost:9043/api/tokens/valid-count',
      { headers }
    );
    console.log(`📊 Valid tokens remaining: ${countResponse.data.validCount}`);
    
  } catch (error) {
    console.error('❌ Failed to save token via API:', error.response?.data || error.message);
    // Fallback to file saving
    await fallbackSaveToFile(accessToken, tenantUrl, emailNote);
  }
}
```

### Error Handling Best Practices

```python
def robust_token_save(access_token, tenant_url, email_note):
    client = TokenAPIClient(auth_token='your_secret_password_here_change_this')
    
    # Try API first
    try:
        result = client.save_token(
            access_token=access_token,
            tenant_url=tenant_url,
            email_note=email_note
        )
        
        if result['success']:
            return True
        else:
            print(f'API save failed: {result.get("message")}')
            
    except Exception as e:
        print(f'API connection failed: {e}')
    
    # Fallback to file saving
    print('Falling back to file saving...')
    return save_to_tokens_json(access_token, tenant_url, email_note)
```
