#!/usr/bin/env python3
"""
Firefox with SOCKS5 Proxy Runner
专门处理 SOCKS5 代理的 Firefox 自动化
"""

import sys
import os
from pathlib import Path

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

from selenium import webdriver
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.common.proxy import Proxy, ProxyType

def create_firefox_with_socks5(proxy_host, proxy_port, proxy_user=None, proxy_pass=None, headless=False):
    """创建配置了 SOCKS5 代理的 Firefox WebDriver"""
    
    print(f"🦊 配置 Firefox SOCKS5 代理: {proxy_user}@{proxy_host}:{proxy_port}")
    
    # 创建 Firefox 选项
    options = FirefoxOptions()
    
    if headless:
        options.add_argument('--headless')
        print("👻 使用无头模式")
    else:
        print("🖥️ 使用有界面模式")
    
    # 创建 Firefox Profile
    profile = webdriver.FirefoxProfile()
    
    # SOCKS5 代理配置
    profile.set_preference('network.proxy.type', 1)  # 手动代理配置
    profile.set_preference('network.proxy.socks', proxy_host)
    profile.set_preference('network.proxy.socks_port', int(proxy_port))
    profile.set_preference('network.proxy.socks_version', 5)
    
    # SOCKS5 认证
    if proxy_user and proxy_pass:
        profile.set_preference('network.proxy.socks_username', proxy_user)
        profile.set_preference('network.proxy.socks_password', proxy_pass)
        print("🔐 已配置 SOCKS5 认证")
    
    # 禁用 DNS 通过代理（可选，根据需要调整）
    profile.set_preference('network.proxy.socks_remote_dns', True)
    
    # 忽略本地地址
    profile.set_preference('network.proxy.no_proxies_on', 'localhost,127.0.0.1')
    
    # 禁用各种提示
    profile.set_preference('dom.webnotifications.enabled', False)
    profile.set_preference('dom.push.enabled', False)
    
    # 设置用户代理
    user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0'
    profile.set_preference('general.useragent.override', user_agent)
    
    # 指纹防护
    profile.set_preference('privacy.resistFingerprinting', True)
    profile.set_preference('webgl.disabled', True)
    profile.set_preference('device.sensors.enabled', False)
    
    # 将 profile 设置到 options
    options.profile = profile
    
    try:
        # 创建 WebDriver
        driver = webdriver.Firefox(options=options)
        print("✅ Firefox SOCKS5 代理配置成功")
        return driver
    except Exception as e:
        print(f"❌ Firefox 启动失败: {e}")
        raise

def test_proxy_connection(driver):
    """测试代理连接"""
    print("🧪 测试代理连接...")
    
    try:
        # 访问 IP 检查网站
        driver.get("https://httpbin.org/ip")
        
        # 等待页面加载
        import time
        time.sleep(3)
        
        # 获取页面内容
        page_source = driver.page_source
        print("📄 IP 检查结果:")
        print(page_source)
        
        # 测试访问目标网站
        print("🌐 测试访问 Augment...")
        driver.get("https://augmentcode.com")
        time.sleep(5)
        
        print("✅ 代理连接测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 代理连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🦊 Firefox SOCKS5 代理测试")
    print("=" * 40)
    
    # 从环境变量或直接配置代理信息
    proxy_host = "sg2.cliproxy.io"  # 你的代理主机
    proxy_port = "3010"             # 你的代理端口
    proxy_user = "lovh89107-region-SG"    # 你的代理用户名
    proxy_pass = "ebmjyzqo"    # 你的代理密码
    
    # 从环境变量读取（如果设置了）
    proxy_host = os.getenv('SOCKS5_HOST', proxy_host)
    proxy_port = os.getenv('SOCKS5_PORT', proxy_port)
    proxy_user = os.getenv('SOCKS5_USER', proxy_user)
    proxy_pass = os.getenv('SOCKS5_PASS', proxy_pass)
    
    print(f"代理配置: {proxy_user}@{proxy_host}:{proxy_port}")
    
    driver = None
    try:
        # 创建 Firefox 实例
        driver = create_firefox_with_socks5(
            proxy_host=proxy_host,
            proxy_port=proxy_port,
            proxy_user=proxy_user,
            proxy_pass=proxy_pass,
            headless=False  # 设为 True 使用无头模式
        )
        
        # 测试代理连接
        if test_proxy_connection(driver):
            print("\n🎉 SOCKS5 代理配置成功！")
            print("🔍 请检查浏览器窗口确认代理工作正常")
            
            # 保持浏览器打开
            input("\n按 Enter 键关闭浏览器...")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        if driver:
            try:
                driver.quit()
                print("🧹 浏览器已关闭")
            except:
                pass

if __name__ == '__main__':
    main()
