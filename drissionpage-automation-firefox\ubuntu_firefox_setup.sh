#!/bin/bash

echo "🦊 Ubuntu Firefox 设置脚本"
echo "=========================="

# 检查是否为 Ubuntu/Linux
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "❌ 此脚本仅适用于 Linux 系统"
    exit 1
fi

echo "🔍 检查当前 Firefox 安装状态..."

# 检查 Firefox 是否已安装
if command -v firefox &> /dev/null; then
    echo "✅ Firefox 已安装"
    FIREFOX_PATH=$(which firefox)
    echo "📍 Firefox 路径: $FIREFOX_PATH"
    
    # 检查版本
    echo "🔍 检查 Firefox 版本..."
    firefox --version
    
    # 测试是否可执行
    if [ -x "$FIREFOX_PATH" ]; then
        echo "✅ Firefox 可执行"
        
        # 导出环境变量
        echo "🔧 设置环境变量..."
        echo "export FIREFOX_BINARY_PATH=\"$FIREFOX_PATH\"" >> ~/.bashrc
        export FIREFOX_BINARY_PATH="$FIREFOX_PATH"
        echo "✅ 已设置 FIREFOX_BINARY_PATH=$FIREFOX_PATH"
        
    else
        echo "❌ Firefox 不可执行"
    fi
else
    echo "❌ Firefox 未安装"
    echo "🔧 正在安装 Firefox..."
    
    # 更新包列表
    sudo apt update
    
    # 安装 Firefox
    sudo apt install -y firefox
    
    # 再次检查
    if command -v firefox &> /dev/null; then
        echo "✅ Firefox 安装成功"
        FIREFOX_PATH=$(which firefox)
        echo "📍 Firefox 路径: $FIREFOX_PATH"
        firefox --version
        
        # 设置环境变量
        echo "export FIREFOX_BINARY_PATH=\"$FIREFOX_PATH\"" >> ~/.bashrc
        export FIREFOX_BINARY_PATH="$FIREFOX_PATH"
        echo "✅ 已设置 FIREFOX_BINARY_PATH=$FIREFOX_PATH"
    else
        echo "❌ Firefox 安装失败"
        exit 1
    fi
fi

echo ""
echo "🔍 检查 GeckoDriver..."

# 检查 GeckoDriver
if command -v geckodriver &> /dev/null; then
    echo "✅ GeckoDriver 已安装"
    geckodriver --version
else
    echo "❌ GeckoDriver 未安装"
    echo "🔧 正在安装 GeckoDriver..."
    
    # 下载最新的 GeckoDriver
    GECKODRIVER_VERSION=$(curl -s https://api.github.com/repos/mozilla/geckodriver/releases/latest | grep -Po '"tag_name": "\K.*?(?=")')
    echo "📥 下载 GeckoDriver $GECKODRIVER_VERSION..."
    
    wget -q "https://github.com/mozilla/geckodriver/releases/download/$GECKODRIVER_VERSION/geckodriver-$GECKODRIVER_VERSION-linux64.tar.gz"
    
    if [ -f "geckodriver-$GECKODRIVER_VERSION-linux64.tar.gz" ]; then
        tar -xzf "geckodriver-$GECKODRIVER_VERSION-linux64.tar.gz"
        sudo mv geckodriver /usr/local/bin/
        sudo chmod +x /usr/local/bin/geckodriver
        rm "geckodriver-$GECKODRIVER_VERSION-linux64.tar.gz"
        
        echo "✅ GeckoDriver 安装成功"
        geckodriver --version
    else
        echo "❌ GeckoDriver 下载失败"
    fi
fi

echo ""
echo "🧪 运行 Firefox 路径测试..."
python3 test_firefox_path.py

echo ""
echo "✅ 设置完成！"
echo ""
echo "💡 使用说明:"
echo "1. 重新加载环境变量: source ~/.bashrc"
echo "2. 或者手动设置: export FIREFOX_BINARY_PATH=\"$(which firefox)\""
echo "3. 然后运行: python3 run_firefox_fixed.py"
echo ""
echo "🔧 如果仍有问题，请手动设置:"
echo "export FIREFOX_BINARY_PATH=\"/usr/bin/firefox\""
