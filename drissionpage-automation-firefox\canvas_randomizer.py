#!/usr/bin/env python3
"""
Canvas 指纹随机化器
专门用于随机化 Canvas 指纹，确保每次生成不同的指纹
"""

import random
from drissionpage_logger import DrissionPageLogger

class CanvasRandomizer:
    """Canvas 指纹随机化器"""
    
    def __init__(self, page, logger=None):
        self.page = page
        self.logger = logger or DrissionPageLogger()
    
    def inject_canvas_randomization(self):
        """注入 Canvas 随机化脚本"""
        try:
            self.logger.log('🎨 注入 Canvas 指纹随机化...')
            
            # 生成随机种子
            random_seed = random.randint(1, 1000000)
            noise_level = random.uniform(0.005, 0.02)  # 随机噪声级别
            
            canvas_script = f"""
            (function() {{
                'use strict';
                
                console.log('🎨 Canvas 指纹随机化器已激活');
                
                // 随机种子
                let randomSeed = {random_seed};
                const noiseLevel = {noise_level};
                
                // 简单的伪随机数生成器
                function seededRandom() {{
                    randomSeed = (randomSeed * 9301 + 49297) % 233280;
                    return randomSeed / 233280;
                }}
                
                // 保存原始方法
                const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
                const originalFillText = CanvasRenderingContext2D.prototype.fillText;
                const originalStrokeText = CanvasRenderingContext2D.prototype.strokeText;
                const originalFillRect = CanvasRenderingContext2D.prototype.fillRect;
                const originalStrokeRect = CanvasRenderingContext2D.prototype.strokeRect;
                
                // 添加噪声到图像数据
                function addNoiseToImageData(imageData) {{
                    const data = imageData.data;
                    for (let i = 0; i < data.length; i += 4) {{
                        if (seededRandom() < noiseLevel) {{
                            // 添加随机噪声到 RGB 通道
                            data[i] = Math.min(255, Math.max(0, data[i] + (seededRandom() * 6 - 3)));     // R
                            data[i + 1] = Math.min(255, Math.max(0, data[i + 1] + (seededRandom() * 6 - 3))); // G
                            data[i + 2] = Math.min(255, Math.max(0, data[i + 2] + (seededRandom() * 6 - 3))); // B
                        }}
                    }}
                    return imageData;
                }}
                
                // 重写 toDataURL 方法
                HTMLCanvasElement.prototype.toDataURL = function(...args) {{
                    const context = this.getContext('2d');
                    if (context) {{
                        try {{
                            // 获取当前图像数据
                            const imageData = context.getImageData(0, 0, this.width, this.height);
                            
                            // 添加噪声
                            const noisyImageData = addNoiseToImageData(imageData);
                            
                            // 将修改后的数据放回 canvas
                            context.putImageData(noisyImageData, 0, 0);
                        }} catch (e) {{
                            // 如果出错，添加一个小的随机矩形
                            const oldFillStyle = context.fillStyle;
                            context.fillStyle = `rgba(${{Math.floor(seededRandom() * 255)}}, ${{Math.floor(seededRandom() * 255)}}, ${{Math.floor(seededRandom() * 255)}}, 0.01)`;
                            context.fillRect(seededRandom() * this.width, seededRandom() * this.height, 1, 1);
                            context.fillStyle = oldFillStyle;
                        }}
                    }}
                    return originalToDataURL.apply(this, args);
                }};
                
                // 重写文本绘制方法，添加微小偏移
                CanvasRenderingContext2D.prototype.fillText = function(text, x, y, maxWidth) {{
                    const offsetX = (seededRandom() - 0.5) * 0.2;
                    const offsetY = (seededRandom() - 0.5) * 0.2;
                    return originalFillText.call(this, text, x + offsetX, y + offsetY, maxWidth);
                }};
                
                CanvasRenderingContext2D.prototype.strokeText = function(text, x, y, maxWidth) {{
                    const offsetX = (seededRandom() - 0.5) * 0.2;
                    const offsetY = (seededRandom() - 0.5) * 0.2;
                    return originalStrokeText.call(this, text, x + offsetX, y + offsetY, maxWidth);
                }};
                
                // 重写矩形绘制方法，添加微小偏移
                CanvasRenderingContext2D.prototype.fillRect = function(x, y, width, height) {{
                    const offsetX = (seededRandom() - 0.5) * 0.1;
                    const offsetY = (seededRandom() - 0.5) * 0.1;
                    return originalFillRect.call(this, x + offsetX, y + offsetY, width, height);
                }};
                
                CanvasRenderingContext2D.prototype.strokeRect = function(x, y, width, height) {{
                    const offsetX = (seededRandom() - 0.5) * 0.1;
                    const offsetY = (seededRandom() - 0.5) * 0.1;
                    return originalStrokeRect.call(this, x + offsetX, y + offsetY, width, height);
                }};
                
                // 监听新创建的 canvas 元素
                const originalCreateElement = document.createElement;
                document.createElement = function(tagName) {{
                    const element = originalCreateElement.call(this, tagName);
                    if (tagName.toLowerCase() === 'canvas') {{
                        // 为新创建的 canvas 添加随机标记
                        element._fingerprintRandomized = true;
                        
                        // 添加一个微小的随机像素
                        setTimeout(() => {{
                            const ctx = element.getContext('2d');
                            if (ctx) {{
                                const oldFillStyle = ctx.fillStyle;
                                ctx.fillStyle = `rgba(${{Math.floor(seededRandom() * 255)}}, ${{Math.floor(seededRandom() * 255)}}, ${{Math.floor(seededRandom() * 255)}}, 0.003)`;
                                ctx.fillRect(seededRandom() * element.width, seededRandom() * element.height, 1, 1);
                                ctx.fillStyle = oldFillStyle;
                            }}
                        }}, 1);
                    }}
                    return element;
                }};
                
                console.log('✅ Canvas 指纹随机化已激活，种子:', randomSeed);
            }})();
            """
            
            # 注入脚本
            self.page.run_js(canvas_script)
            self.logger.log('✅ Canvas 指纹随机化注入成功')
            return True
            
        except Exception as e:
            self.logger.log(f'❌ Canvas 指纹随机化注入失败: {e}')
            return False
    
    def test_canvas_randomization(self, test_count=5):
        """测试 Canvas 随机化效果"""
        try:
            self.logger.log(f'🧪 测试 Canvas 随机化效果 ({test_count} 次)...')
            
            fingerprints = []
            for i in range(test_count):
                fingerprint = self.page.run_js(f"""
                    // 创建测试 Canvas
                    const canvas = document.createElement('canvas');
                    canvas.width = 200;
                    canvas.height = 50;
                    const ctx = canvas.getContext('2d');
                    
                    // 绘制测试内容
                    ctx.textBaseline = 'top';
                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#f60';
                    ctx.fillRect(125, 1, 62, 20);
                    ctx.fillStyle = '#069';
                    ctx.fillText('Canvas Test {i+1} 🎨', 2, 15);
                    ctx.fillStyle = 'rgba(102, 204, 0, 0.7)';
                    ctx.fillText('Random: ' + Math.random(), 4, 35);
                    
                    // 获取指纹
                    return canvas.toDataURL();
                """)
                
                fingerprints.append(fingerprint[:100] + '...')  # 只保留前100个字符用于比较
                self.logger.log(f'   测试 {i+1}: {fingerprints[i]}')
            
            # 分析唯一性
            unique_fingerprints = len(set(fingerprints))
            randomization_effective = unique_fingerprints > 1
            
            self.logger.log(f'🔍 随机化分析: {unique_fingerprints}/{test_count} 个不同指纹')
            
            if randomization_effective:
                self.logger.log('✅ Canvas 指纹随机化有效')
            else:
                self.logger.log('❌ Canvas 指纹随机化无效')
            
            return {
                'fingerprints': fingerprints,
                'unique_count': unique_fingerprints,
                'total_count': test_count,
                'effectiveness': randomization_effective,
                'success_rate': unique_fingerprints / test_count
            }
            
        except Exception as e:
            self.logger.log(f'❌ Canvas 随机化测试失败: {e}')
            return None
    
    def force_canvas_randomization(self):
        """强制激活 Canvas 随机化（在页面加载后调用）"""
        try:
            self.logger.log('🔄 强制激活 Canvas 随机化...')
            
            # 重新注入随机化脚本
            success = self.inject_canvas_randomization()
            
            if success:
                # 立即测试效果
                test_result = self.test_canvas_randomization(3)
                if test_result and test_result['effectiveness']:
                    self.logger.log('✅ Canvas 随机化强制激活成功')
                    return True
                else:
                    self.logger.log('⚠️ Canvas 随机化强制激活后仍无效')
                    return False
            else:
                return False
                
        except Exception as e:
            self.logger.log(f'❌ 强制激活 Canvas 随机化失败: {e}')
            return False
