# Dependencies
/node_modules

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# Mac files
.DS_Store

# VSCode files
.vscode/

# Debug and image directories
debug/
image/

# Real browser automation debug files
real-browser-automation/html/
real-browser-automation/screenshots/

# Logs directory
logs/
real-browser-automation/logs/
*.log

# Token API database and logs
token-api/tokens.db
logs/summary.log