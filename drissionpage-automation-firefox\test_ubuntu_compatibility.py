#!/usr/bin/env python3
"""
Ubuntu 兼容性测试脚本
专门测试在 Ubuntu 环境下的 Chrome 路径检测和端口配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from drissionpage_automation import find_chrome_path, find_available_port
import platform
import subprocess

def test_chrome_detection():
    """测试 Chrome 路径检测"""
    print("🔍 Chrome 路径检测测试")
    print("=" * 40)
    
    # 检测系统
    system = platform.system()
    print(f"操作系统: {system}")
    print(f"系统版本: {platform.platform()}")
    
    # 测试路径检测
    chrome_path = find_chrome_path()
    if chrome_path:
        print(f"✅ 找到 Chrome: {chrome_path}")
        
        # 验证文件是否可执行
        if os.access(chrome_path, os.X_OK):
            print("✅ Chrome 可执行")
        else:
            print("❌ Chrome 不可执行")
            
        # 尝试获取版本信息
        try:
            result = subprocess.run([chrome_path, '--version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ Chrome 版本: {result.stdout.strip()}")
            else:
                print(f"⚠️ 无法获取版本信息: {result.stderr}")
        except Exception as e:
            print(f"⚠️ 版本检测失败: {e}")
    else:
        print("❌ 未找到 Chrome")
        
        # 提供安装建议
        if system == 'Linux':
            print("\n💡 Ubuntu/Linux Chrome 安装建议:")
            print("   sudo apt update")
            print("   sudo apt install google-chrome-stable")
            print("   # 或者")
            print("   sudo snap install chromium")
    
    return chrome_path is not None

def test_port_detection():
    """测试端口检测"""
    print("\n🔌 端口检测测试")
    print("=" * 40)
    
    # 测试多个端口
    test_ports = [9222, 9223, 9224, 9300, 9301]
    
    for port in test_ports:
        available = find_available_port(port, 1)
        if available == port:
            print(f"✅ 端口 {port}: 可用")
        else:
            print(f"❌ 端口 {port}: 被占用，建议使用 {available}")
    
    # 测试动态分配
    dynamic_port = find_available_port()
    print(f"🎯 动态分配端口: {dynamic_port}")
    
    return True

def test_ubuntu_specific():
    """测试 Ubuntu 特有功能"""
    print("\n🐧 Ubuntu 特有功能测试")
    print("=" * 40)

    system = platform.system()
    if system != 'Linux':
        print(f"⚠️ 当前系统是 {system}，跳过 Ubuntu 特有测试")
        return True

    # 检查显示环境
    display = os.environ.get('DISPLAY')
    if display:
        print(f"✅ DISPLAY 环境变量: {display}")
    else:
        print("❌ 未设置 DISPLAY 环境变量")
        print("💡 如果使用 SSH，请启用 X11 转发:")
        print("   ssh -X username@hostname")

    # 测试 PyVirtualDisplay
    try:
        from pyvirtualdisplay import Display
        print("✅ PyVirtualDisplay: 已安装")

        # 测试创建虚拟显示器
        try:
            test_display = Display(visible=False, size=(1024, 768))
            test_display.start()
            print(f"✅ 虚拟显示器测试: 成功 (显示器: {test_display.display})")
            test_display.stop()
        except Exception as e:
            print(f"❌ 虚拟显示器测试: 失败 - {e}")
            print("💡 可能需要安装: sudo apt install xvfb")

    except ImportError:
        print("❌ PyVirtualDisplay: 未安装")
        print("💡 安装命令: pip install PyVirtualDisplay")
        print("💡 系统依赖: sudo apt install xvfb")
    
    # 检查 X11 相关包
    x11_packages = ['xvfb', 'xauth', 'x11-utils']
    for package in x11_packages:
        try:
            result = subprocess.run(['dpkg', '-l', package], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {package}: 已安装")
            else:
                print(f"❌ {package}: 未安装")
        except:
            print(f"⚠️ {package}: 检测失败")
    
    # 检查用户组
    try:
        import grp
        user_groups = [g.gr_name for g in grp.getgrall() if os.getenv('USER') in g.gr_mem]
        if 'video' in user_groups:
            print("✅ 用户在 video 组中")
        else:
            print("⚠️ 用户不在 video 组中，可能影响 GPU 访问")
    except:
        print("⚠️ 无法检查用户组")
    
    return True

def test_chrome_arguments():
    """测试 Chrome 参数兼容性"""
    print("\n⚙️ Chrome 参数兼容性测试")
    print("=" * 40)
    
    chrome_path = find_chrome_path()
    if not chrome_path:
        print("❌ 无法测试，未找到 Chrome")
        return False
    
    # 测试关键参数
    test_args = [
        '--version',
        '--no-sandbox --version',
        '--disable-gpu --version',
        '--accept-lang=en-US --version'
    ]
    
    for args in test_args:
        try:
            cmd = f"{chrome_path} {args}"
            result = subprocess.run(cmd.split(), 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ 参数测试通过: {args.split()[0]}")
            else:
                print(f"❌ 参数测试失败: {args.split()[0]}")
        except Exception as e:
            print(f"⚠️ 参数测试异常: {args.split()[0]} - {e}")
    
    return True

def main():
    """主测试函数"""
    print("🧪 Ubuntu 兼容性测试")
    print("=" * 50)
    
    results = []
    
    # 运行所有测试
    results.append(("Chrome 检测", test_chrome_detection()))
    results.append(("端口检测", test_port_detection()))
    results.append(("Ubuntu 特有功能", test_ubuntu_specific()))
    results.append(("Chrome 参数", test_chrome_arguments()))
    
    # 总结结果
    print("\n📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Ubuntu 兼容性良好")
    else:
        print("⚠️ 部分测试失败，请检查上述建议")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
