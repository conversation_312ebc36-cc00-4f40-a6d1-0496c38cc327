#!/usr/bin/env python3
"""
代理配置测试脚本
测试 DRISSON_ENHANCED_PROXY 配置选项的不同组合
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import DrissionPageConfig

def test_proxy_config_scenarios():
    """测试不同的代理配置场景"""
    print("🧪 代理配置测试")
    print("=" * 50)
    
    # 保存原始环境变量
    original_enhanced = os.environ.get('DRISSON_ENHANCED_PROXY')
    original_drissionpage = os.environ.get('DRISSIONPAGE_PROXY')
    
    test_scenarios = [
        {
            'name': '场景1: DRISSON_ENHANCED_PROXY=false (明确禁用)',
            'enhanced': 'false',
            'drissionpage': 'true',
            'expected': False,
            'description': 'Enhanced 版本明确禁用代理，即使通用配置启用'
        },
        {
            'name': '场景2: DRISSON_ENHANCED_PROXY=true (明确启用)',
            'enhanced': 'true',
            'drissionpage': 'false',
            'expected': True,
            'description': 'Enhanced 版本明确启用代理，即使通用配置禁用'
        },
        {
            'name': '场景3: 未设置 DRISSON_ENHANCED_PROXY，回退到 DRISSIONPAGE_PROXY=true',
            'enhanced': None,
            'drissionpage': 'true',
            'expected': True,
            'description': '使用通用配置作为回退'
        },
        {
            'name': '场景4: 未设置 DRISSON_ENHANCED_PROXY，回退到 DRISSIONPAGE_PROXY=false',
            'enhanced': None,
            'drissionpage': 'false',
            'expected': False,
            'description': '使用通用配置作为回退'
        },
        {
            'name': '场景5: 都未设置（默认值）',
            'enhanced': None,
            'drissionpage': None,
            'expected': False,
            'description': '默认禁用代理'
        }
    ]
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        
        # 设置环境变量
        if scenario['enhanced'] is not None:
            os.environ['DRISSON_ENHANCED_PROXY'] = scenario['enhanced']
        elif 'DRISSON_ENHANCED_PROXY' in os.environ:
            del os.environ['DRISSON_ENHANCED_PROXY']
            
        if scenario['drissionpage'] is not None:
            os.environ['DRISSIONPAGE_PROXY'] = scenario['drissionpage']
        elif 'DRISSIONPAGE_PROXY' in os.environ:
            del os.environ['DRISSIONPAGE_PROXY']
        
        # 创建配置实例（会重新读取环境变量）
        try:
            config = DrissionPageConfig()
            actual = config.drissionpage_proxy
            
            # 验证结果
            if actual == scenario['expected']:
                status = "✅ 通过"
                results.append(True)
            else:
                status = "❌ 失败"
                results.append(False)
            
            print(f"   DRISSON_ENHANCED_PROXY: {os.environ.get('DRISSON_ENHANCED_PROXY', 'not set')}")
            print(f"   DRISSIONPAGE_PROXY: {os.environ.get('DRISSIONPAGE_PROXY', 'not set')}")
            print(f"   期望结果: {scenario['expected']}")
            print(f"   实际结果: {actual}")
            print(f"   测试状态: {status}")
            
        except Exception as e:
            print(f"   ❌ 配置创建失败: {e}")
            results.append(False)
    
    # 恢复原始环境变量
    if original_enhanced is not None:
        os.environ['DRISSON_ENHANCED_PROXY'] = original_enhanced
    elif 'DRISSON_ENHANCED_PROXY' in os.environ:
        del os.environ['DRISSON_ENHANCED_PROXY']
        
    if original_drissionpage is not None:
        os.environ['DRISSIONPAGE_PROXY'] = original_drissionpage
    elif 'DRISSIONPAGE_PROXY' in os.environ:
        del os.environ['DRISSIONPAGE_PROXY']
    
    # 总结结果
    print(f"\n📊 测试结果总结")
    print("=" * 50)
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total} 项测试")
    
    if passed == total:
        print("🎉 所有测试通过！代理配置逻辑正确")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置逻辑")
        return False

def test_current_config():
    """测试当前的配置"""
    print("\n🔧 当前配置测试")
    print("=" * 50)
    
    config = DrissionPageConfig()
    config.print_config()
    
    print(f"\n📋 环境变量状态:")
    print(f"   DRISSON_ENHANCED_PROXY: {os.environ.get('DRISSON_ENHANCED_PROXY', 'not set')}")
    print(f"   DRISSIONPAGE_PROXY: {os.environ.get('DRISSIONPAGE_PROXY', 'not set')}")
    
    return config

def main():
    """主测试函数"""
    print("🧪 DrissionPage Enhanced 代理配置测试")
    print("=" * 60)
    
    # 测试当前配置
    current_config = test_current_config()
    
    # 测试不同场景
    scenarios_passed = test_proxy_config_scenarios()
    
    # 最终总结
    print(f"\n🎯 最终总结")
    print("=" * 60)
    print(f"当前代理状态: {'启用' if current_config.drissionpage_proxy else '禁用'}")
    print(f"配置逻辑测试: {'✅ 通过' if scenarios_passed else '❌ 失败'}")
    
    if scenarios_passed:
        print("\n💡 使用说明:")
        print("   - 设置 DRISSON_ENHANCED_PROXY=true 启用 Enhanced 版本代理")
        print("   - 设置 DRISSON_ENHANCED_PROXY=false 禁用 Enhanced 版本代理")
        print("   - 不设置 DRISSON_ENHANCED_PROXY 则使用 DRISSIONPAGE_PROXY 作为回退")
        print("   - 这样可以独立控制 Enhanced 版本的代理设置")
    
    return scenarios_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
