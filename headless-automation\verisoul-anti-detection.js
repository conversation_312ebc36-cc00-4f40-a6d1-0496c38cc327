/**
 * Verisoul 专用反检测系统
 * 针对 Verisoul 反欺诈平台的高级对抗策略
 */

const crypto = require('crypto');

class VerisoulAntiDetection {
    constructor() {
        this.isEnabled = true;
        this.behaviorSimulationActive = false;
        this.fingerprintMasks = this.generateFingerprintMasks();
    }

    /**
     * 生成一致的指纹掩码
     */
    generateFingerprintMasks() {
        const seed = Date.now().toString();
        const hash = crypto.createHash('md5').update(seed).digest('hex');
        
        return {
            hardwareConcurrency: 4 + (parseInt(hash.substr(0, 2), 16) % 8),
            deviceMemory: [4, 8, 16][parseInt(hash.substr(2, 2), 16) % 3],
            maxTouchPoints: parseInt(hash.substr(4, 1), 16) % 2,
            colorDepth: [24, 32][parseInt(hash.substr(5, 1), 16) % 2],
            pixelDepth: [24, 32][parseInt(hash.substr(6, 1), 16) % 2],
            screenWidth: 1920 + (parseInt(hash.substr(7, 2), 16) % 200 - 100),
            screenHeight: 1080 + (parseInt(hash.substr(9, 2), 16) % 200 - 100),
            timezoneOffset: -480 + (parseInt(hash.substr(11, 2), 16) % 24 - 12) * 60,
            language: ['en-US', 'en-GB', 'en-SG', 'en-AU'][parseInt(hash.substr(13, 1), 16) % 4],
            platform: ['Win32', 'MacIntel', 'Linux x86_64'][parseInt(hash.substr(14, 1), 16) % 3]
        };
    }

    /**
     * 在页面加载前注入反检测脚本
     */
    async injectPreloadScript(page) {
        console.log('🛡️ 注入 Verisoul 反检测预加载脚本...');
        
        await page.evaluateOnNewDocument((masks) => {
            // 1. 核心指纹伪装
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => masks.hardwareConcurrency,
                configurable: true
            });

            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => masks.deviceMemory,
                configurable: true
            });

            Object.defineProperty(navigator, 'maxTouchPoints', {
                get: () => masks.maxTouchPoints,
                configurable: true
            });

            // 2. 屏幕信息伪装
            Object.defineProperty(screen, 'width', {
                get: () => masks.screenWidth,
                configurable: true
            });

            Object.defineProperty(screen, 'height', {
                get: () => masks.screenHeight,
                configurable: true
            });

            Object.defineProperty(screen, 'colorDepth', {
                get: () => masks.colorDepth,
                configurable: true
            });

            Object.defineProperty(screen, 'pixelDepth', {
                get: () => masks.pixelDepth,
                configurable: true
            });

            // 3. 时区和语言伪装
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {
                return masks.timezoneOffset;
            };

            Object.defineProperty(navigator, 'language', {
                get: () => masks.language,
                configurable: true
            });

            Object.defineProperty(navigator, 'languages', {
                get: () => [masks.language, 'en'],
                configurable: true
            });

            // 4. 平台信息伪装
            Object.defineProperty(navigator, 'platform', {
                get: () => masks.platform,
                configurable: true
            });

            // 5. WebDriver 检测对抗
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                configurable: true
            });

            // 删除 webdriver 相关属性
            delete navigator.__proto__.webdriver;

            // 6. Chrome 对象伪装
            if (!window.chrome) {
                window.chrome = {
                    runtime: {
                        onConnect: undefined,
                        onMessage: undefined
                    },
                    loadTimes: function() {
                        return {
                            commitLoadTime: Date.now() / 1000 - Math.random() * 10,
                            connectionInfo: 'http/1.1',
                            finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 5,
                            finishLoadTime: Date.now() / 1000 - Math.random() * 3,
                            firstPaintAfterLoadTime: Date.now() / 1000 - Math.random() * 2,
                            firstPaintTime: Date.now() / 1000 - Math.random() * 8,
                            navigationType: 'Other',
                            npnNegotiatedProtocol: 'h2',
                            requestTime: Date.now() / 1000 - Math.random() * 15,
                            startLoadTime: Date.now() / 1000 - Math.random() * 12,
                            wasAlternateProtocolAvailable: false,
                            wasFetchedViaSpdy: true,
                            wasNpnNegotiated: true
                        };
                    },
                    csi: function() {
                        return {
                            onloadT: Date.now(),
                            pageT: Math.random() * 1000,
                            startE: Date.now() - Math.random() * 5000,
                            tran: Math.floor(Math.random() * 20)
                        };
                    }
                };
            }

            // 7. 插件列表伪装
            Object.defineProperty(navigator, 'plugins', {
                get: () => {
                    const plugins = [
                        {
                            name: 'Chrome PDF Plugin',
                            filename: 'internal-pdf-viewer',
                            description: 'Portable Document Format'
                        },
                        {
                            name: 'Chrome PDF Viewer',
                            filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
                            description: ''
                        },
                        {
                            name: 'Native Client',
                            filename: 'internal-nacl-plugin',
                            description: ''
                        }
                    ];
                    
                    Object.defineProperty(plugins, 'length', {
                        get: () => plugins.length
                    });
                    
                    return plugins;
                },
                configurable: true
            });

            // 8. 权限 API 伪装
            if (navigator.permissions && navigator.permissions.query) {
                const originalQuery = navigator.permissions.query;
                navigator.permissions.query = function(parameters) {
                    return originalQuery.call(this, parameters).then(result => {
                        if (parameters.name === 'notifications') {
                            Object.defineProperty(result, 'state', {
                                get: () => 'default'
                            });
                        }
                        return result;
                    });
                };
            }

            console.log('🛡️ Verisoul 反检测预加载脚本已注入');
        }, this.fingerprintMasks);
    }

    /**
     * 在 Verisoul 加载后应用高级对抗
     */
    async applyAdvancedCountermeasures(page) {
        console.log('🎯 应用 Verisoul 高级对抗措施...');
        
        await page.evaluate(() => {
            // 1. 事件监听器劫持
            const originalAddEventListener = EventTarget.prototype.addEventListener;
            EventTarget.prototype.addEventListener = function(type, listener, options) {
                if (typeof listener === 'function') {
                    const wrappedListener = function(event) {
                        // 为鼠标和键盘事件添加随机延迟
                        if (type.includes('mouse') || type.includes('key') || type.includes('touch')) {
                            const delay = Math.random() * 5;
                            setTimeout(() => {
                                try {
                                    listener.call(this, event);
                                } catch (e) {
                                    // 静默处理错误
                                }
                            }, delay);
                        } else {
                            try {
                                listener.call(this, event);
                            } catch (e) {
                                // 静默处理错误
                            }
                        }
                    };
                    return originalAddEventListener.call(this, type, wrappedListener, options);
                }
                return originalAddEventListener.call(this, type, listener, options);
            };

            // 2. Canvas 指纹干扰
            const originalGetContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
                const context = originalGetContext.call(this, contextType, contextAttributes);
                
                if (contextType === '2d') {
                    const originalGetImageData = context.getImageData;
                    context.getImageData = function(sx, sy, sw, sh) {
                        const imageData = originalGetImageData.call(this, sx, sy, sw, sh);
                        // 添加微小的随机噪声
                        for (let i = 0; i < imageData.data.length; i += 4) {
                            if (Math.random() < 0.001) {
                                imageData.data[i] = Math.min(255, imageData.data[i] + Math.floor(Math.random() * 3) - 1);
                                imageData.data[i + 1] = Math.min(255, imageData.data[i + 1] + Math.floor(Math.random() * 3) - 1);
                                imageData.data[i + 2] = Math.min(255, imageData.data[i + 2] + Math.floor(Math.random() * 3) - 1);
                            }
                        }
                        return imageData;
                    };
                }
                
                return context;
            };

            // 3. WebGL 指纹干扰
            const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                const result = originalGetParameter.call(this, parameter);
                
                // 对特定参数添加随机变化
                if (parameter === this.RENDERER) {
                    const renderers = [
                        'ANGLE (Intel, Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.100.8681)',
                        'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5671)',
                        'ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0, D3D11-27.20.1020.2002)'
                    ];
                    return renderers[Math.floor(Math.random() * renderers.length)];
                }
                
                return result;
            };

            // 4. AudioContext 指纹干扰
            if (window.AudioContext || window.webkitAudioContext) {
                const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
                const originalCreateOscillator = OriginalAudioContext.prototype.createOscillator;
                
                OriginalAudioContext.prototype.createOscillator = function() {
                    const oscillator = originalCreateOscillator.call(this);
                    const originalStart = oscillator.start;
                    
                    oscillator.start = function(when) {
                        // 添加微小的随机延迟
                        const randomDelay = Math.random() * 0.001;
                        return originalStart.call(this, when ? when + randomDelay : randomDelay);
                    };
                    
                    return oscillator;
                };
            }

            console.log('🎯 Verisoul 高级对抗措施已应用');
        });
    }

    /**
     * 启动行为模拟
     */
    async startBehaviorSimulation(page) {
        if (this.behaviorSimulationActive) return;
        
        console.log('🎭 启动真实用户行为模拟...');
        this.behaviorSimulationActive = true;

        // 在页面中启动行为模拟
        await page.evaluate(() => {
            let simulationActive = true;
            
            // 随机鼠标移动
            const mouseSimulation = () => {
                if (!simulationActive) return;
                
                const x = Math.random() * window.innerWidth;
                const y = Math.random() * window.innerHeight;
                
                const event = new MouseEvent('mousemove', {
                    clientX: x,
                    clientY: y,
                    bubbles: true,
                    cancelable: true
                });
                
                document.dispatchEvent(event);
                
                setTimeout(mouseSimulation, 2000 + Math.random() * 3000);
            };
            
            // 随机滚动
            const scrollSimulation = () => {
                if (!simulationActive) return;
                
                const scrollAmount = (Math.random() - 0.5) * 200;
                window.scrollBy(0, scrollAmount);
                
                setTimeout(scrollSimulation, 5000 + Math.random() * 10000);
            };
            
            // 随机焦点变化
            const focusSimulation = () => {
                if (!simulationActive) return;
                
                const focusableElements = document.querySelectorAll('input, button, a, textarea, select');
                if (focusableElements.length > 0) {
                    const randomElement = focusableElements[Math.floor(Math.random() * focusableElements.length)];
                    if (randomElement && typeof randomElement.focus === 'function') {
                        try {
                            randomElement.focus();
                            setTimeout(() => randomElement.blur(), 100 + Math.random() * 500);
                        } catch (e) {
                            // 静默处理错误
                        }
                    }
                }
                
                setTimeout(focusSimulation, 10000 + Math.random() * 20000);
            };
            
            // 启动所有模拟
            setTimeout(mouseSimulation, 1000);
            setTimeout(scrollSimulation, 2000);
            setTimeout(focusSimulation, 3000);
            
            // 存储停止函数
            window._stopBehaviorSimulation = () => {
                simulationActive = false;
            };
        });
    }

    /**
     * 停止行为模拟
     */
    async stopBehaviorSimulation(page) {
        if (!this.behaviorSimulationActive) return;
        
        console.log('🛑 停止行为模拟...');
        this.behaviorSimulationActive = false;
        
        try {
            await page.evaluate(() => {
                if (window._stopBehaviorSimulation) {
                    window._stopBehaviorSimulation();
                }
            });
        } catch (e) {
            // 静默处理错误
        }
    }

    /**
     * 检测 Verisoul 是否已加载
     */
    async isVerisoulLoaded(page) {
        return await page.evaluate(() => {
            return !!(window.Verisoul && typeof window.Verisoul.session === 'function');
        });
    }

    /**
     * 等待 Verisoul 加载并应用对抗措施
     */
    async waitForVerisoulAndApplyCountermeasures(page, timeout = 30000) {
        console.log('⏳ 等待 Verisoul 加载...');
        
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const isLoaded = await this.isVerisoulLoaded(page);
            
            if (isLoaded) {
                console.log('✅ Verisoul 已加载，应用对抗措施...');
                await this.applyAdvancedCountermeasures(page);
                await this.startBehaviorSimulation(page);
                return true;
            }
            
            await new Promise(resolve => setTimeout(resolve, 500));
        }
        
        console.log('⚠️ Verisoul 加载超时，继续执行...');
        return false;
    }
}

module.exports = VerisoulAntiDetection;
