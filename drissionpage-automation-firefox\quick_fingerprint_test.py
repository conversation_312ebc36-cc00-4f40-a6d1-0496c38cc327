#!/usr/bin/env python3
"""
快速指纹防护验证脚本
快速测试指纹防护是否生效
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from drissionpage_automation import DrissionPageAutomation

def quick_fingerprint_test():
    """快速指纹防护测试"""
    print("⚡ 快速指纹防护验证")
    print("=" * 50)
    
    automation = None
    try:
        # 初始化
        print("🚀 启动浏览器...")
        automation = DrissionPageAutomation()
        automation.init_browser()
        
        # 导航到简单页面进行测试
        print("📄 加载测试页面...")
        automation.page.get('data:text/html,<html><body><h1>Fingerprint Test</h1></body></html>')
        
        # 等待防护激活
        time.sleep(2)
        
        # 执行指纹检测
        print("🔍 检测指纹防护状态...")
        
        fingerprint_data = automation.page.run_js("""
            const data = {
                // 基础检测
                webdriver: navigator.webdriver,
                chrome: !!window.chrome,
                platform: navigator.platform,
                language: navigator.language,
                hardwareConcurrency: navigator.hardwareConcurrency,
                
                // 屏幕信息
                screen: {
                    width: screen.width,
                    height: screen.height,
                    colorDepth: screen.colorDepth
                },
                
                // 插件信息
                plugins: navigator.plugins.length,
                
                // Canvas 指纹测试
                canvas: (() => {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    ctx.textBaseline = 'top';
                    ctx.font = '14px Arial';
                    ctx.fillText('Test', 2, 2);
                    return canvas.toDataURL().slice(0, 50) + '...';
                })(),
                
                // WebGL 信息
                webgl: (() => {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl');
                        if (!gl) return 'Not supported';
                        return {
                            vendor: gl.getParameter(gl.VENDOR),
                            renderer: gl.getParameter(gl.RENDERER)
                        };
                    } catch(e) {
                        return 'Error: ' + e.message;
                    }
                })(),
                
                // 时区信息
                timezone: {
                    offset: new Date().getTimezoneOffset(),
                    resolved: Intl.DateTimeFormat().resolvedOptions().timeZone
                }
            };
            
            return data;
        """)
        
        # 分析结果
        print("\n📊 指纹防护分析结果:")
        print("=" * 50)
        
        # WebDriver 检测
        webdriver_hidden = fingerprint_data['webdriver'] is None or fingerprint_data['webdriver'] == False
        print(f"🤖 WebDriver 隐藏: {'✅ 是' if webdriver_hidden else '❌ 否'} ({fingerprint_data['webdriver']})")
        
        # Chrome 对象
        chrome_present = fingerprint_data['chrome']
        print(f"🌐 Chrome 对象: {'✅ 存在' if chrome_present else '❌ 缺失'}")
        
        # 设备信息
        print(f"💻 平台: {fingerprint_data['platform']}")
        print(f"🌍 语言: {fingerprint_data['language']}")
        print(f"🖥️ 屏幕: {fingerprint_data['screen']['width']}x{fingerprint_data['screen']['height']}")
        print(f"⚙️ 硬件并发: {fingerprint_data['hardwareConcurrency']}")
        print(f"🔌 插件数量: {fingerprint_data['plugins']}")
        
        # Canvas 指纹
        print(f"🎨 Canvas 指纹: {fingerprint_data['canvas']}")
        
        # WebGL 信息
        if isinstance(fingerprint_data['webgl'], dict):
            print(f"🎮 WebGL 供应商: {fingerprint_data['webgl']['vendor']}")
            print(f"🎮 WebGL 渲染器: {fingerprint_data['webgl']['renderer']}")
        else:
            print(f"🎮 WebGL: {fingerprint_data['webgl']}")
        
        # 时区信息
        print(f"🕐 时区偏移: {fingerprint_data['timezone']['offset']}")
        print(f"🌏 时区: {fingerprint_data['timezone']['resolved']}")
        
        # 多次 Canvas 测试（检查随机性）
        print(f"\n🎨 Canvas 随机性测试:")
        canvas_fingerprints = []
        for i in range(3):
            canvas_fp = automation.page.run_js("""
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Random Test ' + Math.random(), 2, 2);
                return canvas.toDataURL();
            """)
            canvas_fingerprints.append(canvas_fp[:50] + '...')
            print(f"   测试 {i+1}: {canvas_fingerprints[i]}")
            time.sleep(0.5)
        
        unique_canvas = len(set(canvas_fingerprints))
        canvas_randomized = unique_canvas > 1
        print(f"🎨 Canvas 随机化: {'✅ 有效' if canvas_randomized else '❌ 无效'} ({unique_canvas}/3 不同)")
        
        # 总体评估
        print(f"\n🎯 防护效果评估:")
        print("=" * 50)
        
        protection_score = 0
        total_checks = 4
        
        if webdriver_hidden:
            protection_score += 1
            print("✅ WebDriver 属性已隐藏")
        else:
            print("❌ WebDriver 属性未隐藏")
        
        if chrome_present:
            protection_score += 1
            print("✅ Chrome 对象已伪装")
        else:
            print("❌ Chrome 对象缺失")
        
        if canvas_randomized:
            protection_score += 1
            print("✅ Canvas 指纹已随机化")
        else:
            print("❌ Canvas 指纹未随机化")
        
        # 检查是否有明显的自动化特征
        automation_detected = False
        if fingerprint_data['webdriver'] is True:
            automation_detected = True
        if fingerprint_data['plugins'] == 0:
            automation_detected = True
        
        if not automation_detected:
            protection_score += 1
            print("✅ 无明显自动化特征")
        else:
            print("❌ 检测到自动化特征")
        
        protection_percentage = (protection_score / total_checks) * 100
        
        print(f"\n📊 防护评分: {protection_score}/{total_checks} ({protection_percentage:.0f}%)")
        
        if protection_percentage >= 75:
            print("🎉 指纹防护效果良好！")
            status = "excellent"
        elif protection_percentage >= 50:
            print("⚠️ 指纹防护效果一般，建议优化")
            status = "moderate"
        else:
            print("❌ 指纹防护效果较差，需要改进")
            status = "poor"
        
        return {
            'status': status,
            'score': protection_score,
            'total': total_checks,
            'percentage': protection_percentage,
            'data': fingerprint_data
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None
    finally:
        if automation and automation.page:
            automation.cleanup()

def main():
    """主函数"""
    print("⚡ 快速指纹防护验证工具")
    print("=" * 60)
    
    result = quick_fingerprint_test()
    
    if result:
        print(f"\n💡 建议:")
        if result['status'] == 'excellent':
            print("   - 当前防护配置良好，继续保持")
            print("   - 定期测试确保防护效果")
        elif result['status'] == 'moderate':
            print("   - 检查插件是否正确加载")
            print("   - 验证高级防护脚本是否生效")
            print("   - 考虑添加更多防护措施")
        else:
            print("   - 检查 DRISSON_FINGERPRINT_PROTECTION 配置")
            print("   - 确认 Canvas Fingerprint Defender 插件已下载")
            print("   - 验证浏览器启动参数")
            print("   - 运行完整的配置测试")
        
        print(f"\n🔧 故障排除:")
        print("   - 运行: python test_enhanced_config.py")
        print("   - 运行: python test_canvas_fingerprint_defender.py")
        print("   - 检查日志文件获取详细信息")
        
        return result['status'] != 'poor'
    else:
        print("\n❌ 测试失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
