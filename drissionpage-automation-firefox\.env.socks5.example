# SOCKS5 代理配置示例
# 复制此文件为 .env 并配置你的 SOCKS5 代理信息

# ===== SOCKS5 代理配置 =====
# SOCKS5 代理主机
SOCKS5_HOST=sg2.cliproxy.io

# SOCKS5 代理端口
SOCKS5_PORT=3010

# SOCKS5 用户名
SOCKS5_USER=your_username

# SOCKS5 密码
SOCKS5_PASS=your_password

# ===== Firefox 配置 =====
# Firefox 显示模式
DRISSON_FIREFOX_HEADFULL=true

# 启用代理
DRISSON_FIREFOX_PROXY=true

# ===== 通用代理配置 (用于兼容现有代码) =====
# 代理类型标识
PROXY_TYPE=socks5

# 代理 URL (SOCKS5 格式)
PROXY_URL=socks5://sg2.cliproxy.io:3010

# 代理用户名和密码
PROXY_USER=your_username
PROXY_PASS=your_password

# ===== 其他配置 =====
# 指纹防护
DRISSON_FINGERPRINT_PROTECTION=true

# 调试模式
DEBUG_MODE=true

# 保存截图和HTML
SAVE_SCREENSHOTS=true
SAVE_HTML=true

# 超时配置
PAGE_TIMEOUT=30000
EMAIL_CHECK_TIMEOUT=120000

# ===== 使用说明 =====
# 1. 将此文件复制为 .env
# 2. 修改 SOCKS5_* 配置为你的实际代理信息
# 3. 运行: python run_firefox_with_socks5.py
# 4. 或运行完整自动化: python drissionpage_automation.py
