# Token API Documentation

Welcome to the Token API documentation. This directory contains comprehensive documentation for the Token API system.

## 📚 Documentation Index

### [API Documentation](./API_DOCUMENTATION.md)
Complete API reference with detailed endpoint specifications, request/response formats, and status codes.

**Contents:**
- All API endpoints with full specifications
- Request/response examples
- Authentication details
- Error handling
- Status codes reference

### [Usage Examples](./USAGE_EXAMPLES.md)
Practical examples and code snippets for different programming languages and use cases.

**Contents:**
- JavaScript/Node.js examples
- Python examples with client class
- cURL commands
- Integration patterns for automation scripts
- Error handling best practices

### [Quick Reference](./QUICK_REFERENCE.md)
Condensed reference guide for quick lookups and common operations.

**Contents:**
- Endpoint summary table
- One-liner commands for cURL, Python, JavaScript
- Response format templates
- Configuration file locations
- Server management commands

## 🚀 Getting Started

1. **Start the API server:**
   ```bash
   npm run token-api
   ```

2. **Test the health endpoint:**
   ```bash
   curl http://localhost:9043/health
   ```

3. **Check valid token count:**
   ```bash
   curl -H "Authorization: Bearer your_secret_password_here_change_this" \
        http://localhost:9043/api/tokens/valid-count
   ```

## 🔧 Configuration

The API uses the following configuration files:

- **`config.json`** - Main configuration (token validity, automation settings)
- **`.env`** - Environment variables (AUTH_PASSWORD, TOKEN_API_PORT)
- **`tokens.db`** - SQLite database (auto-created)

## 📊 Key Features

- **SQLite Database** - Persistent token storage
- **Bearer Authentication** - Secure API access
- **Token Validation** - 7-day validity with 2-hour buffer
- **Scheduled Tasks** - Automatic token count monitoring
- **Automation Integration** - Trigger Python scripts when tokens are low
- **Comprehensive Logging** - Activity tracking in summary.log

## 🔗 Related Files

- **Main API Server:** `../token-api.js`
- **Configuration:** `../config.json`
- **Migration Script:** `../migrate-tokens.js`
- **Integration Examples:** `../example-automation-integration.py`

## 📝 Quick Examples

### Save a Token (Python)
```python
import requests

headers = {'Authorization': 'Bearer your_secret_password_here_change_this'}
data = {
    'access_token': 'your_token_here',
    'tenant_url': 'https://d1.api.augmentcode.com/'
}

response = requests.post(
    'http://localhost:9043/api/tokens/save',
    headers=headers,
    json=data
)
print(response.json())
```

### Get Valid Token Count (JavaScript)
```javascript
const axios = require('axios');

const response = await axios.get(
    'http://localhost:9043/api/tokens/valid-count',
    {
        headers: {
            'Authorization': 'Bearer your_secret_password_here_change_this'
        }
    }
);

console.log('Valid tokens:', response.data.validCount);
```

### Get Unused Token (cURL)
```bash
curl -H "Authorization: Bearer your_secret_password_here_change_this" \
     http://localhost:9043/api/tokens
```

## 🆘 Support

For issues or questions:

1. Check the [API Documentation](./API_DOCUMENTATION.md) for endpoint details
2. Review [Usage Examples](./USAGE_EXAMPLES.md) for implementation patterns
3. Use [Quick Reference](./QUICK_REFERENCE.md) for fast lookups
4. Check `logs/summary.log` for system activity
5. Verify server status with health endpoint

## 🔄 Migration

If you have existing tokens in `tokens.json`, use the migration script:

```bash
npm run migrate-tokens
```

This will transfer all tokens to the SQLite database while preserving all data and metadata.
