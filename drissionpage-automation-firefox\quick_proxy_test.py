#!/usr/bin/env python3
"""
Quick Proxy Test for Firefox
快速测试 Firefox 代理配置
"""

from selenium import webdriver
from selenium.webdriver.firefox.options import Options as FirefoxOptions
import time
import os

def test_firefox_proxy():
    """测试 Firefox 代理配置"""
    
    print("🦊 Firefox 代理测试")
    print("=" * 30)
    
    # 代理配置 - 请修改为你的实际代理信息
    PROXY_HOST = "sg2.cliproxy.io"
    PROXY_PORT = 3010
    PROXY_USER = "your_username"  # 替换为你的用户名
    PROXY_PASS = "your_password"  # 替换为你的密码
    
    print(f"代理: {PROXY_USER}@{PROXY_HOST}:{PROXY_PORT}")
    
    # 创建 Firefox 选项
    options = FirefoxOptions()
    
    # 创建 Firefox Profile
    profile = webdriver.FirefoxProfile()
    
    # 方法1: 使用 SOCKS5 代理（推荐）
    print("🔧 配置 SOCKS5 代理...")
    profile.set_preference('network.proxy.type', 1)
    profile.set_preference('network.proxy.socks', PROXY_HOST)
    profile.set_preference('network.proxy.socks_port', PROXY_PORT)
    profile.set_preference('network.proxy.socks_version', 5)
    profile.set_preference('network.proxy.socks_username', PROXY_USER)
    profile.set_preference('network.proxy.socks_password', PROXY_PASS)
    profile.set_preference('network.proxy.socks_remote_dns', True)
    
    # 禁用认证提示
    profile.set_preference('network.automatic-ntlm-auth.allow-proxies', True)
    profile.set_preference('signon.autologin.proxy', True)
    
    # 忽略本地地址
    profile.set_preference('network.proxy.no_proxies_on', 'localhost,127.0.0.1')
    
    # 禁用各种提示
    profile.set_preference('dom.webnotifications.enabled', False)
    profile.set_preference('browser.safebrowsing.malware.enabled', False)
    profile.set_preference('browser.safebrowsing.phishing.enabled', False)
    
    # 设置 profile
    options.profile = profile
    
    driver = None
    try:
        print("🚀 启动 Firefox...")
        driver = webdriver.Firefox(options=options)
        
        print("🌐 测试代理连接...")
        
        # 测试1: 检查 IP
        print("📍 检查当前 IP...")
        driver.get("https://httpbin.org/ip")
        time.sleep(3)
        
        ip_info = driver.page_source
        print("IP 信息:")
        print(ip_info)
        
        # 测试2: 访问目标网站
        print("\n🎯 访问 Augment...")
        driver.get("https://augmentcode.com")
        time.sleep(5)
        
        title = driver.title
        print(f"页面标题: {title}")
        
        if "Augment" in title or len(title) > 0:
            print("✅ 代理连接成功！")
        else:
            print("⚠️ 页面可能未正确加载")
        
        # 保持浏览器打开
        print("\n🔍 请检查浏览器窗口")
        input("按 Enter 键关闭浏览器...")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        
        # 如果 SOCKS5 失败，尝试 HTTP 代理
        print("\n🔄 尝试 HTTP 代理...")
        try_http_proxy(PROXY_HOST, PROXY_PORT, PROXY_USER, PROXY_PASS)
        
    finally:
        if driver:
            driver.quit()
            print("🧹 浏览器已关闭")

def try_http_proxy(host, port, user, password):
    """尝试 HTTP 代理"""
    
    options = FirefoxOptions()
    profile = webdriver.FirefoxProfile()
    
    # HTTP 代理配置
    profile.set_preference('network.proxy.type', 1)
    profile.set_preference('network.proxy.http', host)
    profile.set_preference('network.proxy.http_port', port)
    profile.set_preference('network.proxy.ssl', host)
    profile.set_preference('network.proxy.ssl_port', port)
    
    # 尝试设置认证
    profile.set_preference('network.automatic-ntlm-auth.trusted-uris', host)
    profile.set_preference('network.automatic-ntlm-auth.allow-proxies', True)
    profile.set_preference('signon.autologin.proxy', True)
    
    options.profile = profile
    
    driver = None
    try:
        driver = webdriver.Firefox(options=options)
        
        print("🌐 测试 HTTP 代理...")
        driver.get("https://httpbin.org/ip")
        time.sleep(3)
        
        print("HTTP 代理测试结果:")
        print(driver.page_source)
        
        input("按 Enter 键关闭...")
        
    except Exception as e:
        print(f"❌ HTTP 代理也失败: {e}")
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    print("⚠️ 请先修改脚本中的代理配置信息！")
    print("编辑 quick_proxy_test.py 文件，设置正确的:")
    print("- PROXY_HOST")
    print("- PROXY_PORT") 
    print("- PROXY_USER")
    print("- PROXY_PASS")
    print()
    
    confirm = input("已配置代理信息？(y/N): ")
    if confirm.lower() == 'y':
        test_firefox_proxy()
    else:
        print("请先配置代理信息后再运行")
