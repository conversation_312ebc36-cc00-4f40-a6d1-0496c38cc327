#!/usr/bin/env python3
"""
Ubuntu Firefox 问题修复脚本
"""

import os
import subprocess
import sys

def run_command(cmd):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def main():
    print("🦊 Ubuntu Firefox 问题修复")
    print("=" * 40)
    
    # 1. 检查 Firefox 是否安装
    print("🔍 检查 Firefox 安装状态...")
    success, output, error = run_command("which firefox")
    
    if success and output:
        firefox_path = output
        print(f"✅ 找到 Firefox: {firefox_path}")

        # 检查是否是 snap 包装器
        success, file_info, _ = run_command(f"file {firefox_path}")
        if success and file_info:
            print(f"📄 文件类型: {file_info}")

            # 如果是脚本或链接，尝试找到真实的 Firefox 二进制文件
            if "script" in file_info.lower() or "symbolic link" in file_info.lower():
                print("🔍 检测到脚本或链接，查找真实的 Firefox 二进制文件...")

                # 尝试查找 snap 中的 Firefox
                success, snap_path, _ = run_command("find /snap/firefox -name firefox -type f 2>/dev/null | head -1")
                if success and snap_path:
                    print(f"🎯 找到 snap Firefox: {snap_path}")
                    firefox_path = snap_path
                else:
                    # 尝试查找系统中的 Firefox
                    success, sys_path, _ = run_command("find /usr -name firefox-bin -type f 2>/dev/null | head -1")
                    if success and sys_path:
                        print(f"🎯 找到系统 Firefox: {sys_path}")
                        firefox_path = sys_path
                    else:
                        # 尝试查找 firefox-esr
                        success, esr_path, _ = run_command("find /usr -name firefox-esr -type f 2>/dev/null | head -1")
                        if success and esr_path:
                            print(f"🎯 找到 Firefox ESR: {esr_path}")
                            firefox_path = esr_path

        # 检查版本
        success, version, _ = run_command(f"{firefox_path} --version")
        if success:
            print(f"✅ Firefox 版本: {version}")

        # 设置环境变量
        print("🔧 设置环境变量...")
        os.environ['FIREFOX_BINARY_PATH'] = firefox_path
        
        # 写入到 .bashrc
        bashrc_line = f'export FIREFOX_BINARY_PATH="{firefox_path}"'
        try:
            with open(os.path.expanduser('~/.bashrc'), 'a') as f:
                f.write(f'\n# Firefox binary path for automation\n{bashrc_line}\n')
            print(f"✅ 已添加到 ~/.bashrc: {bashrc_line}")
        except Exception as e:
            print(f"⚠️ 无法写入 ~/.bashrc: {e}")
        
    else:
        print("❌ Firefox 未找到，尝试安装...")
        
        # 尝试安装 Firefox
        print("📦 更新包列表...")
        run_command("sudo apt update")
        
        print("📦 安装 Firefox...")
        success, _, error = run_command("sudo apt install -y firefox")
        
        if success:
            print("✅ Firefox 安装成功")
            # 重新检查
            success, output, _ = run_command("which firefox")
            if success:
                firefox_path = output
                print(f"✅ Firefox 路径: {firefox_path}")
                os.environ['FIREFOX_BINARY_PATH'] = firefox_path
        else:
            print(f"❌ Firefox 安装失败: {error}")
            return False
    
    # 2. 检查 GeckoDriver
    print("\n🔍 检查 GeckoDriver...")
    success, output, _ = run_command("which geckodriver")
    
    if success and output:
        print(f"✅ 找到 GeckoDriver: {output}")
        success, version, _ = run_command("geckodriver --version")
        if success:
            print(f"✅ GeckoDriver 版本: {version.split()[1] if len(version.split()) > 1 else version}")
    else:
        print("❌ GeckoDriver 未找到")
        print("💡 请手动安装 GeckoDriver:")
        print("   wget https://github.com/mozilla/geckodriver/releases/download/v0.33.0/geckodriver-v0.33.0-linux64.tar.gz")
        print("   tar -xzf geckodriver-v0.33.0-linux64.tar.gz")
        print("   sudo mv geckodriver /usr/local/bin/")
        print("   sudo chmod +x /usr/local/bin/geckodriver")
    
    # 3. 测试 Firefox 启动
    print("\n🧪 测试 Firefox 启动...")
    if 'FIREFOX_BINARY_PATH' in os.environ:
        firefox_path = os.environ['FIREFOX_BINARY_PATH']
        success, _, error = run_command(f"{firefox_path} --headless --version")
        if success:
            print("✅ Firefox headless 模式测试成功")
        else:
            print(f"❌ Firefox headless 模式测试失败: {error}")
    
    print("\n✅ 修复完成！")
    print("\n🚀 现在可以运行:")
    print("   source ~/.bashrc")
    print("   python3 run_firefox_fixed.py")
    
    return True

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断")
    except Exception as e:
        print(f"\n❌ 修复失败: {e}")
        sys.exit(1)
