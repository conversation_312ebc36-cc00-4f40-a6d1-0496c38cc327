# Python脚本调用处理机制详解

## 当前实现分析

### 🔍 Token计算来源
**确认：完全从SQLite数据库计算，不再使用tokens.json**

```sql
SELECT COUNT(*) as count FROM tokens 
WHERE used = 0 AND created_at > datetime(?)
```

- ✅ 只统计未使用的token (`used = 0`)
- ✅ 只统计有效期内的token (7天-2小时缓冲)
- ✅ 完全独立于tokens.json文件

### 🚀 当前Python脚本调用机制

```javascript
function triggerAutomationScript(count = 1, reason = 'Scheduled check') {
  const scriptPath = path.join(__dirname, config.automationScript.workingDirectory);
  
  for (let i = 0; i < count; i++) {
    const child = spawn(config.automationScript.command, config.automationScript.args, {
      cwd: scriptPath,
      detached: true,    // 独立进程
      stdio: 'ignore'    // 忽略所有输入输出
    });
    
    child.unref();       // 立即释放，不等待结果
    console.log(`[AUTOMATION] Started script instance ${i + 1}/${count} (PID: ${child.pid})`);
  }
  
  logSummary(`${reason}: Triggered automation script ${count} time(s)`);
}
```

### 📋 实际执行过程

1. **命令**: `python3 run_firefox_fixed.py`
2. **工作目录**: `../drissionpage-automation-firefox`
3. **进程模式**: 
   - `detached: true` - 独立进程，不依赖父进程
   - `stdio: 'ignore'` - 完全忽略输出
   - `child.unref()` - 立即释放引用

4. **并发执行**: 如果需要3个token，会同时启动3个Python进程

## ⚠️ 当前机制的问题

### 1. 并发冲突风险
```bash
# 同时运行多个实例可能导致：
python3 run_firefox_fixed.py  # 进程1
python3 run_firefox_fixed.py  # 进程2  
python3 run_firefox_fixed.py  # 进程3
```

**潜在问题**:
- 多个浏览器实例争夺相同端口
- 同时使用相同邮箱地址
- 代理连接冲突
- 文件写入冲突

### 2. 无状态反馈
- ❌ 不知道脚本是否成功
- ❌ 不知道是否生成了token
- ❌ 无法处理失败重试
- ❌ 无法监控进程状态

### 3. 资源管理问题
- 无法控制最大并发数
- 无法设置超时
- 无法紧急停止失控进程

## 💡 改进方案

### 方案1: 顺序执行 (推荐)

```javascript
// 改进的处理机制
class AutomationHandler {
  async triggerAutomationScript(count = 1, reason = 'Scheduled check') {
    for (let i = 0; i < count; i++) {
      await this.runSingleScript(i + 1, count, reason);
      // 等待5秒避免冲突
      if (i < count - 1) {
        await this.wait(5000);
      }
    }
  }
  
  runSingleScript(current, total, reason) {
    return new Promise((resolve, reject) => {
      const child = spawn('python3', ['run_firefox_fixed.py'], {
        cwd: scriptPath,
        stdio: ['ignore', 'pipe', 'pipe']  // 捕获输出
      });
      
      // 设置5分钟超时
      const timeout = setTimeout(() => {
        child.kill('SIGTERM');
        reject(new Error('Script timeout'));
      }, 300000);
      
      child.on('close', (code) => {
        clearTimeout(timeout);
        if (code === 0) {
          console.log(`Script ${current}/${total} completed successfully`);
          resolve();
        } else {
          reject(new Error(`Script failed with code ${code}`));
        }
      });
    });
  }
}
```

### 方案2: 队列管理

```javascript
class ScriptQueue {
  constructor() {
    this.queue = [];
    this.running = false;
    this.maxConcurrent = 1;  // 最大并发数
  }
  
  addToQueue(count, reason) {
    for (let i = 0; i < count; i++) {
      this.queue.push({ id: Date.now() + i, reason });
    }
    this.processQueue();
  }
  
  async processQueue() {
    if (this.running || this.queue.length === 0) return;
    
    this.running = true;
    while (this.queue.length > 0) {
      const task = this.queue.shift();
      await this.executeScript(task);
      await this.wait(5000);  // 5秒间隔
    }
    this.running = false;
  }
}
```

## 🔧 Python脚本修改建议

### 1. 添加退出码
```python
# 在run_firefox_fixed.py中
import sys

try:
    # 自动化逻辑
    if token_generated_successfully:
        print("✅ Token generated and saved successfully")
        sys.exit(0)  # 成功退出
    else:
        print("❌ Failed to generate token")
        sys.exit(1)  # 失败退出
except Exception as e:
    print(f"❌ Script error: {e}")
    sys.exit(2)  # 异常退出
```

### 2. 添加进程锁
```python
import fcntl
import os

# 防止多个实例同时运行
lock_file = '/tmp/firefox_automation.lock'
try:
    lock_fd = os.open(lock_file, os.O_CREAT | os.O_EXCL | os.O_RDWR)
    # 运行自动化逻辑
finally:
    os.close(lock_fd)
    os.unlink(lock_file)
```

### 3. 输出标准化
```python
import json

def log_result(success, message, token_id=None):
    result = {
        "success": success,
        "message": message,
        "timestamp": time.time(),
        "token_id": token_id
    }
    print(f"AUTOMATION_RESULT: {json.dumps(result)}")

# 使用示例
if token_saved:
    log_result(True, "Token saved successfully", token_id)
else:
    log_result(False, "Failed to save token")
```

## 📊 监控和调试

### 1. 进程监控
```javascript
// 获取运行状态
app.get('/api/automation/status', authenticateToken, (req, res) => {
  res.json({
    runningProcesses: automationHandler.getRunningCount(),
    queuedTasks: automationHandler.getQueueLength(),
    lastExecution: automationHandler.getLastExecution()
  });
});
```

### 2. 日志增强
```javascript
// 详细日志记录
function logAutomationEvent(event, details) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event: event,
    details: details
  };
  
  fs.appendFileSync('logs/automation.log', JSON.stringify(logEntry) + '\n');
}
```

## 🎯 推荐实施步骤

1. **立即改进**: 实施顺序执行，避免并发冲突
2. **短期改进**: 添加输出捕获和超时处理
3. **中期改进**: 修改Python脚本添加标准化输出
4. **长期改进**: 实施完整的队列管理和监控系统

这样可以确保Python脚本调用更加可靠和可控。
