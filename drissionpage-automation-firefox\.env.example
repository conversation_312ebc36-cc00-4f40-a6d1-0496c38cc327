# Firefox Automation Configuration
# 复制此文件为 .env 并根据需要修改配置

# ===== Firefox 专用配置 =====
# Firefox 显示模式 (true=显示浏览器窗口, false=无头模式)
DRISSON_FIREFOX_HEADFULL=true

# Firefox 代理配置 (true=启用, false=禁用)
DRISSON_FIREFOX_PROXY=false

# ===== 通用配置 (如果没有设置 Firefox 专用配置，将使用这些作为回退) =====
# 通用显示模式
DRISSIONPAGE_HEADFULL=false

# 通用代理配置
DRISSIONPAGE_PROXY=false

# ===== 代理设置 =====
# 代理服务器地址 (格式: host:port)
PROXY_URL=your-proxy-host:port

# 代理用户名
PROXY_USER=your-proxy-username

# 代理密码
PROXY_PASS=your-proxy-password

# ===== 验证码服务 =====
# YesCaptcha 客户端密钥 (用于自动解决验证码)
YESCAPTCHA_CLIENT_KEY=your-yescaptcha-key

# 是否启用验证码自动解决
DRISSIONPAGE_RECAPTCHA_SOLVE=false

# ===== 指纹防护 =====
# 是否启用指纹防护 (Firefox 使用内置的 privacy.resistFingerprinting)
DRISSON_FINGERPRINT_PROTECTION=true

# ===== 调试配置 =====
# 调试模式
DEBUG_MODE=true

# 是否保存截图
SAVE_SCREENSHOTS=true

# 是否保存HTML
SAVE_HTML=true

# ===== 超时配置 =====
# 页面加载超时 (毫秒)
PAGE_TIMEOUT=30000

# 邮箱检查超时 (毫秒)
EMAIL_CHECK_TIMEOUT=120000

# 邮箱检查间隔 (毫秒)
EMAIL_CHECK_INTERVAL=5000

# ===== 配置说明 =====
# 1. Firefox 专用配置优先级最高
# 2. 如果没有设置 Firefox 专用配置，将使用通用配置
# 3. headfull=true 显示浏览器窗口，适合调试
# 4. headfull=false 无头模式，适合服务器环境
# 5. 代理配置需要同时设置 PROXY_URL, PROXY_USER, PROXY_PASS
# 6. Firefox 的指纹防护使用浏览器内置功能，比 Chrome 扩展更稳定
