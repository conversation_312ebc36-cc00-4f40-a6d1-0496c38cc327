{"name": "shell", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "email-verification": "node headless-automation/run-email-verification.js", "real-browser-verification": "node real-browser-automation/run-real-browser-verification.js", "start": "node headless-automation/index.js", "token-api": "node token-api/token-api.js", "start-token-api": "node token-api/start-token-api.js", "migrate-tokens": "node token-api/migrate-tokens.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.10.0", "clipboardy": "^4.0.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "imapflow": "^1.0.191", "mailparser": "^3.7.4", "nodemailer": "^7.0.5", "puppeteer": "^24.12.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-real-browser": "^1.4.3", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}}