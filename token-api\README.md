# Token API Documentation

## Overview

The Token API provides a centralized system for managing authentication tokens with SQLite database storage, automatic token validation, and scheduled automation script triggering.

## Features

- **SQLite Database**: Persistent token storage with automatic table creation
- **Token Validation**: Automatic validation based on 7-day expiry with 2-hour buffer
- **Scheduled Tasks**: Hourly checks to maintain minimum token count
- **Automation Integration**: Automatic triggering of Python scripts when token count is low
- **Bearer Authentication**: Secure API access with configurable password
- **Comprehensive Logging**: Summary logs for monitoring and debugging

## Configuration

Configuration is managed through `config.json`:

```json
{
  "minValidTokens": 6,
  "tokenValidityDays": 7,
  "validityBufferHours": 2,
  "checkIntervalHours": 1,
  "automationScript": {
    "command": "python3",
    "args": ["run_firefox_fixed.py"],
    "workingDirectory": "../drissionpage-automation-firefox"
  },
  "database": {
    "path": "./tokens.db"
  },
  "logging": {
    "summaryLogPath": "../logs/summary.log"
  }
}
```

## Environment Variables

Required in `.env` file:
- `AUTH_PASSWORD`: Bearer token for API authentication
- `TOKEN_API_PORT`: Port number (default: 9043)

## API Endpoints

### Health Check
```
GET /health
```
Returns server status (no authentication required).

### Get Unused Token
```
GET /api/tokens
Authorization: Bearer {AUTH_PASSWORD}
```
Returns and marks as used the oldest unused token.

### Save New Token
```
POST /api/tokens/save
Authorization: Bearer {AUTH_PASSWORD}
Content-Type: application/json

{
  "access_token": "string (required)",
  "tenant_url": "string (required)",
  "description": "string (optional)",
  "email_note": "string (optional)",
  "user_agent": "string (optional)",
  "session_id": "string (optional)",
  "created_timestamp": "number (optional)"
}
```

### Get Valid Token Count
```
GET /api/tokens/valid-count
Authorization: Bearer {AUTH_PASSWORD}
```
Returns count of tokens that are unused and still valid (within 7 days minus 2 hour buffer).

### Get Token Statistics
```
GET /api/tokens/stats
Authorization: Bearer {AUTH_PASSWORD}
```
Returns comprehensive token statistics including total, used, unused, and valid counts.

### Trigger Automation Script
```
POST /api/tokens/trigger-automation
Authorization: Bearer {AUTH_PASSWORD}
Content-Type: application/json

{
  "count": 1
}
```
Manually triggers the automation script the specified number of times.

## Database Schema

```sql
CREATE TABLE tokens (
  id TEXT PRIMARY KEY,
  access_token TEXT NOT NULL,
  tenant_url TEXT NOT NULL,
  description TEXT,
  email_note TEXT,
  user_agent TEXT,
  session_id TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  used BOOLEAN DEFAULT 0,
  created_timestamp INTEGER
);
```

## Usage Examples

### Starting the Server
```bash
npm run token-api
# or
npm run start-token-api
```

### Migrating Existing Tokens
```bash
npm run migrate-tokens
```

### Example: Save Token from Automation Script
```python
import requests
import json

def save_token_to_api(access_token, tenant_url, email_note=None):
    url = "http://localhost:9043/api/tokens/save"
    headers = {
        "Authorization": "Bearer your_secret_password_here_change_this",
        "Content-Type": "application/json"
    }
    data = {
        "access_token": access_token,
        "tenant_url": tenant_url,
        "email_note": email_note,
        "description": "Firefox token from fixed automation",
        "user_agent": "firefox-fixed-automation",
        "session_id": f"firefox_fixed_{int(time.time())}"
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()
```

## Scheduled Tasks

The system automatically:
1. Checks token count every hour
2. Triggers automation script if valid tokens < minimum required
3. Logs all activities to summary.log

## Logging

Summary logs are written to `../logs/summary.log` with format:
```
2025-08-19T01:47:09.962Z - Server started - 32 valid tokens available
2025-08-19T02:47:09.962Z - Scheduled check: 28 valid tokens found (minimum required: 6)
```

## Error Handling

All endpoints return standardized error responses:
```json
{
  "success": false,
  "error": "Error type",
  "message": "Detailed error message",
  "timestamp": "2025-08-19T01:47:09.962Z"
}
```

## Security

- Bearer token authentication for all protected endpoints
- Input validation for all POST requests
- SQL injection protection through parameterized queries
- Process isolation for automation script execution
