#!/usr/bin/env python3
"""
Verisoul 逆向工程集成测试脚本
用于验证 DrissionPage + Verisoul 逆向工程的集成效果
"""

import time
import json
import logging
from drissionpage_automation import DrissionPageAutomation

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class VerisoulIntegrationTest:
    """Verisoul 集成测试类"""
    
    def __init__(self):
        self.automation = None
        self.test_results = {}
        
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始 Verisoul 逆向工程集成测试...")
        
        try:
            # 1. 测试浏览器启动和初始化
            self.test_browser_initialization()
            
            # 2. 测试工具注入
            self.test_tool_injection()
            
            # 3. 测试逆向分析功能
            self.test_reverse_analysis()
            
            # 4. 测试对抗措施
            self.test_countermeasures()
            
            # 5. 测试状态监控
            self.test_status_monitoring()
            
            # 6. 生成测试报告
            self.generate_test_report()
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
            
        finally:
            if self.automation:
                try:
                    self.automation.cleanup()
                except:
                    pass
    
    def test_browser_initialization(self):
        """测试浏览器启动和 Verisoul 模块初始化"""
        print("\n🚀 测试 1: 浏览器启动和初始化...")
        
        try:
            self.automation = DrissionPageAutomation()
            
            # 启动浏览器
            browser_started = self.automation.start_browser()
            
            # 检查 Verisoul 逆向工程模块是否初始化
            verisoul_initialized = self.automation.verisoul_reverse_engineering is not None
            
            self.test_results['browser_initialization'] = {
                'browser_started': browser_started,
                'verisoul_initialized': verisoul_initialized,
                'success': browser_started and verisoul_initialized
            }
            
            if self.test_results['browser_initialization']['success']:
                print("✅ 浏览器启动和 Verisoul 模块初始化成功")
            else:
                print("❌ 浏览器启动或 Verisoul 模块初始化失败")
                
        except Exception as e:
            print(f"❌ 浏览器初始化测试失败: {e}")
            self.test_results['browser_initialization'] = {'success': False, 'error': str(e)}
    
    def test_tool_injection(self):
        """测试工具注入功能"""
        print("\n🔧 测试 2: 工具注入...")
        
        try:
            # 导航到测试页面
            test_url = "https://augmentcode.com/auth/login"
            navigation_success = self.automation.navigate_to_page(test_url)
            
            if not navigation_success:
                print("❌ 页面导航失败")
                self.test_results['tool_injection'] = {'success': False, 'error': 'Navigation failed'}
                return
            
            # 等待页面加载
            time.sleep(3)
            
            # 检查工具是否注入成功
            analyzer_injected = self.automation.page.run_js("return typeof window.verisoulAnalyzer !== 'undefined';")
            countermeasures_injected = self.automation.page.run_js("return typeof window.verisoulCountermeasures !== 'undefined';")
            
            self.test_results['tool_injection'] = {
                'navigation_success': navigation_success,
                'analyzer_injected': analyzer_injected,
                'countermeasures_injected': countermeasures_injected,
                'success': analyzer_injected and countermeasures_injected
            }
            
            if self.test_results['tool_injection']['success']:
                print("✅ 工具注入成功")
                print(f"   - 逆向分析工具: {'✅' if analyzer_injected else '❌'}")
                print(f"   - 对抗措施工具: {'✅' if countermeasures_injected else '❌'}")
            else:
                print("❌ 工具注入失败")
                
        except Exception as e:
            print(f"❌ 工具注入测试失败: {e}")
            self.test_results['tool_injection'] = {'success': False, 'error': str(e)}
    
    def test_reverse_analysis(self):
        """测试逆向分析功能"""
        print("\n🔍 测试 3: 逆向分析功能...")
        
        try:
            # 启动逆向分析
            analysis_started = self.automation.verisoul_reverse_engineering.start_analysis()
            
            # 等待分析运行
            time.sleep(2)
            
            # 检查分析状态
            analysis_active = self.automation.page.run_js("return window.verisoulAnalyzer.isAnalyzing;")
            
            # 生成分析报告
            report = self.automation.verisoul_reverse_engineering.generate_analysis_report()
            
            self.test_results['reverse_analysis'] = {
                'analysis_started': analysis_started,
                'analysis_active': analysis_active,
                'report_generated': report is not None,
                'success': analysis_started and analysis_active
            }
            
            if self.test_results['reverse_analysis']['success']:
                print("✅ 逆向分析功能正常")
                if report:
                    print(f"   - 检测到函数: {len(report.get('detectedFunctions', []))}")
                    print(f"   - 网络请求: {len(report.get('networkRequests', []))}")
            else:
                print("❌ 逆向分析功能异常")
                
        except Exception as e:
            print(f"❌ 逆向分析测试失败: {e}")
            self.test_results['reverse_analysis'] = {'success': False, 'error': str(e)}
    
    def test_countermeasures(self):
        """测试对抗措施功能"""
        print("\n🛡️ 测试 4: 对抗措施功能...")
        
        try:
            # 激活对抗措施
            countermeasures_activated = self.automation.verisoul_reverse_engineering.activate_countermeasures()
            
            # 等待对抗措施生效
            time.sleep(2)
            
            # 检查对抗措施状态
            status = self.automation.verisoul_reverse_engineering.get_countermeasures_status()
            
            # 检查关键指纹是否被伪装
            webdriver_hidden = self.automation.page.run_js("return navigator.webdriver === undefined;")
            hardware_spoofed = self.automation.page.run_js("return navigator.hardwareConcurrency >= 4 && navigator.hardwareConcurrency <= 16;")
            
            self.test_results['countermeasures'] = {
                'countermeasures_activated': countermeasures_activated,
                'status_active': status.get('isActive', False) if status else False,
                'webdriver_hidden': webdriver_hidden,
                'hardware_spoofed': hardware_spoofed,
                'success': countermeasures_activated and webdriver_hidden and hardware_spoofed
            }
            
            if self.test_results['countermeasures']['success']:
                print("✅ 对抗措施功能正常")
                print(f"   - WebDriver 隐藏: {'✅' if webdriver_hidden else '❌'}")
                print(f"   - 硬件信息伪装: {'✅' if hardware_spoofed else '❌'}")
                if status:
                    print(f"   - 对抗状态: {'激活' if status.get('isActive') else '未激活'}")
            else:
                print("❌ 对抗措施功能异常")
                
        except Exception as e:
            print(f"❌ 对抗措施测试失败: {e}")
            self.test_results['countermeasures'] = {'success': False, 'error': str(e)}
    
    def test_status_monitoring(self):
        """测试状态监控功能"""
        print("\n📊 测试 5: 状态监控功能...")
        
        try:
            # 获取分析报告
            analysis_report = self.automation.verisoul_reverse_engineering.generate_analysis_report()
            
            # 获取对抗状态
            countermeasures_status = self.automation.verisoul_reverse_engineering.get_countermeasures_status()
            
            # 检查浏览器控制台日志
            console_logs = self.automation.page.run_js("""
                return window.console._logs || [];
            """)
            
            self.test_results['status_monitoring'] = {
                'analysis_report_available': analysis_report is not None,
                'countermeasures_status_available': countermeasures_status is not None,
                'console_logs_available': console_logs is not None,
                'success': analysis_report is not None and countermeasures_status is not None
            }
            
            if self.test_results['status_monitoring']['success']:
                print("✅ 状态监控功能正常")
                print(f"   - 分析报告: {'可用' if analysis_report else '不可用'}")
                print(f"   - 对抗状态: {'可用' if countermeasures_status else '不可用'}")
            else:
                print("❌ 状态监控功能异常")
                
        except Exception as e:
            print(f"❌ 状态监控测试失败: {e}")
            self.test_results['status_monitoring'] = {'success': False, 'error': str(e)}
    
    def test_fingerprint_spoofing(self):
        """测试指纹伪装效果"""
        print("\n🎭 测试指纹伪装效果...")
        
        try:
            # 获取当前指纹信息
            fingerprint = self.automation.page.run_js("""
                return {
                    webdriver: navigator.webdriver,
                    hardwareConcurrency: navigator.hardwareConcurrency,
                    deviceMemory: navigator.deviceMemory,
                    maxTouchPoints: navigator.maxTouchPoints,
                    platform: navigator.platform,
                    language: navigator.language,
                    languages: navigator.languages,
                    pluginsLength: navigator.plugins.length,
                    hasChrome: !!window.chrome
                };
            """)
            
            # 验证指纹伪装效果
            checks = {
                'webdriver_hidden': fingerprint.get('webdriver') is None,
                'hardware_reasonable': 4 <= fingerprint.get('hardwareConcurrency', 0) <= 16,
                'memory_reasonable': fingerprint.get('deviceMemory') in [4, 8, 16],
                'chrome_present': fingerprint.get('hasChrome', False),
                'plugins_present': fingerprint.get('pluginsLength', 0) > 0
            }
            
            success_count = sum(checks.values())
            total_checks = len(checks)
            
            print(f"指纹伪装检查结果 ({success_count}/{total_checks}):")
            for check_name, passed in checks.items():
                status = '✅' if passed else '❌'
                print(f"   - {check_name}: {status}")
            
            print(f"\n当前指纹信息:")
            for key, value in fingerprint.items():
                print(f"   - {key}: {value}")
                
        except Exception as e:
            print(f"❌ 指纹伪装测试失败: {e}")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📋 测试报告")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"成功率: {(passed_tests / total_tests * 100):.1f}%")
        
        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = '✅ 通过' if result.get('success', False) else '❌ 失败'
            print(f"   - {test_name}: {status}")
            if not result.get('success', False) and 'error' in result:
                print(f"     错误: {result['error']}")
        
        # 运行指纹伪装测试
        self.test_fingerprint_spoofing()
        
        print("\n🎯 集成测试完成!")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！Verisoul 逆向工程集成成功！")
        else:
            print("⚠️ 部分测试失败，请检查相关功能。")
        
        # 保存测试结果
        try:
            with open('verisoul_integration_test_results.json', 'w', encoding='utf-8') as f:
                json.dump(self.test_results, f, indent=2, ensure_ascii=False)
            print("📄 测试结果已保存到 verisoul_integration_test_results.json")
        except Exception as e:
            print(f"⚠️ 保存测试结果失败: {e}")

def main():
    """主函数"""
    test = VerisoulIntegrationTest()
    test.run_all_tests()

if __name__ == "__main__":
    main()
