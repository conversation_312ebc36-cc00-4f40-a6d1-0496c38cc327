#!/usr/bin/env python3
"""
快速测试 Verisoul 逆向工程修复
"""

import sys
import time
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent.parent))

def test_verisoul_integration():
    """测试 Verisoul 集成是否修复"""
    print("🧪 测试 Verisoul 逆向工程集成修复...")
    
    try:
        # 1. 测试导入
        print("📦 测试模块导入...")
        from drissionpage_automation import DrissionPageAutomation
        from verisoul_reverse_engineering import VerisoulReverseEngineering
        print("✅ 模块导入成功")
        
        # 2. 测试初始化
        print("🚀 测试初始化...")
        automation = DrissionPageAutomation()
        print("✅ DrissionPageAutomation 初始化成功")
        
        # 3. 测试浏览器启动
        print("🌐 测试浏览器启动...")
        browser_started = automation.start_browser()
        if browser_started:
            print("✅ 浏览器启动成功")
            
            # 4. 测试 Verisoul 模块初始化
            if automation.verisoul_reverse_engineering:
                print("✅ Verisoul 逆向工程模块初始化成功")
                
                # 5. 测试页面导航和工具注入
                print("📄 测试页面导航和工具注入...")
                try:
                    # 导航到一个简单的测试页面
                    test_url = "https://www.google.com"
                    navigation_success = automation.navigate_to_page(test_url)
                    
                    if navigation_success:
                        print("✅ 页面导航成功")
                        print("✅ Verisoul 工具注入成功")
                        
                        # 6. 测试工具功能
                        print("🔧 测试工具功能...")
                        
                        # 测试分析启动
                        analysis_started = automation.verisoul_reverse_engineering.start_analysis()
                        if analysis_started:
                            print("✅ 逆向分析启动成功")
                        
                        # 测试对抗措施激活
                        countermeasures_activated = automation.verisoul_reverse_engineering.activate_countermeasures()
                        if countermeasures_activated:
                            print("✅ 对抗措施激活成功")
                        
                        # 测试状态获取
                        status = automation.verisoul_reverse_engineering.get_countermeasures_status()
                        if status:
                            print(f"✅ 对抗措施状态: {status}")
                        
                        print("🎉 所有测试通过！Verisoul 逆向工程集成修复成功！")
                        
                    else:
                        print("❌ 页面导航失败")
                        
                except Exception as e:
                    print(f"❌ 页面导航测试失败: {e}")
                    
            else:
                print("❌ Verisoul 逆向工程模块未初始化")
                
        else:
            print("❌ 浏览器启动失败")
            
        # 清理
        print("🧹 清理资源...")
        automation.cleanup()
        print("✅ 清理完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("🔧 Verisoul 逆向工程集成修复测试")
    print("=" * 50)
    
    success = test_verisoul_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 修复测试成功！可以正常使用 Verisoul 逆向工程功能")
        print("\n📋 使用方法:")
        print("   python drissionpage_automation.py")
        print("   或")
        print("   python run_drissionpage_verification.py")
    else:
        print("❌ 修复测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
