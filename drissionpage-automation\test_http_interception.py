#!/usr/bin/env python3
"""
Verisoul HTTP 拦截功能测试脚本
验证 HTTP 拦截器的功能和效果
"""

import sys
import time
import json
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent.parent))

def test_http_interception():
    """测试 HTTP 拦截功能"""
    print("🎯 测试 Verisoul HTTP 拦截功能...")
    
    try:
        # 1. 测试导入
        print("📦 测试模块导入...")
        from drissionpage_automation import DrissionPageAutomation
        from verisoul_http_interceptor import VerisoulHttpInterceptor
        print("✅ 模块导入成功")
        
        # 2. 测试初始化
        print("🚀 测试初始化...")
        automation = DrissionPageAutomation()
        print("✅ DrissionPageAutomation 初始化成功")
        
        # 3. 测试浏览器启动
        print("🌐 测试浏览器启动...")
        browser_started = automation.start_browser()
        if browser_started:
            print("✅ 浏览器启动成功")
            
            # 4. 测试 HTTP 拦截器初始化
            if automation.verisoul_http_interceptor:
                print("✅ HTTP 拦截器初始化成功")
                
                # 5. 测试页面导航和拦截器激活
                print("📄 测试页面导航和拦截器激活...")
                try:
                    # 导航到测试页面
                    test_url = "https://www.google.com"
                    navigation_success = automation.navigate_to_page(test_url)
                    
                    if navigation_success:
                        print("✅ 页面导航成功")
                        print("✅ HTTP 拦截器自动激活成功")
                        
                        # 6. 测试拦截器功能
                        print("🔧 测试拦截器功能...")
                        
                        # 检查拦截器状态
                        status = automation.verisoul_http_interceptor.get_status()
                        print(f"✅ 拦截器状态: {status}")
                        
                        # 7. 测试模拟 Verisoul 请求
                        print("🧪 测试模拟 Verisoul 请求...")
                        test_requests = [
                            "https://api.verisoul.ai/session",
                            "https://net.prod.verisoul.ai/verify",
                            "wss://net.prod.verisoul.ai/ws"
                        ]
                        
                        for test_url in test_requests:
                            try:
                                # 使用 JavaScript 发送测试请求
                                result = automation.page.run_js(f"""
                                    return new Promise((resolve) => {{
                                        fetch('{test_url}')
                                            .then(response => response.json())
                                            .then(data => resolve({{
                                                success: true,
                                                url: '{test_url}',
                                                data: data
                                            }}))
                                            .catch(error => resolve({{
                                                success: false,
                                                url: '{test_url}',
                                                error: error.message
                                            }}));
                                    }});
                                """)
                                
                                if result and result.get('success'):
                                    print(f"✅ 成功拦截请求: {test_url}")
                                    print(f"   响应数据: {result.get('data', {})}")
                                else:
                                    print(f"⚠️ 请求处理异常: {test_url}")
                                    
                            except Exception as e:
                                print(f"⚠️ 测试请求失败: {test_url} - {e}")
                        
                        # 8. 获取拦截统计
                        print("📊 获取拦截统计...")
                        stats = automation.verisoul_http_interceptor.get_interception_stats()
                        if stats:
                            print(f"✅ 拦截统计: {stats}")
                        else:
                            print("⚠️ 无法获取拦截统计")
                        
                        # 9. 测试自定义响应
                        print("🎭 测试自定义响应...")
                        automation.verisoul_http_interceptor.add_custom_response(
                            '/test-endpoint',
                            {'test': True, 'custom': 'response'}
                        )
                        print("✅ 自定义响应添加成功")
                        
                        # 10. 测试成功验证模拟
                        print("🎉 测试成功验证模拟...")
                        automation.verisoul_http_interceptor.simulate_successful_verification()
                        print("✅ 成功验证模拟完成")
                        
                        print("🎉 所有 HTTP 拦截功能测试通过！")
                        
                    else:
                        print("❌ 页面导航失败")
                        
                except Exception as e:
                    print(f"❌ 页面导航测试失败: {e}")
                    
            else:
                print("❌ HTTP 拦截器未初始化")
                
        else:
            print("❌ 浏览器启动失败")
            
        # 清理
        print("🧹 清理资源...")
        automation.cleanup()
        print("✅ 清理完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_interception_in_real_scenario():
    """在真实场景中测试拦截效果"""
    print("\n🎯 真实场景拦截测试...")
    
    try:
        from drissionpage_automation import DrissionPageAutomation
        
        automation = DrissionPageAutomation()
        automation.start_browser()
        
        # 导航到 Augment 登录页面
        print("🌐 导航到 Augment 登录页面...")
        success = automation.navigate_to_page("https://auth.augmentcode.com/authorize?response_type=code&client_id=v&code_challenge=test&code_challenge_method=S256&state=test&prompt=login")
        
        if success:
            print("✅ 页面导航成功")
            
            # 等待一段时间让 Verisoul 尝试加载
            print("⏳ 等待 Verisoul 加载并观察拦截效果...")
            time.sleep(5)
            
            # 检查拦截统计
            stats = automation.verisoul_http_interceptor.get_interception_stats()
            if stats:
                total = stats.get('totalIntercepted', 0)
                print(f"🚫 成功拦截 {total} 个 Verisoul 请求")
                
                if total > 0:
                    by_type = stats.get('byType', {})
                    for req_type, count in by_type.items():
                        print(f"   - {req_type}: {count} 个请求")
                    
                    recent = stats.get('recentRequests', [])
                    if recent:
                        print("📋 最近拦截的请求:")
                        for req in recent[-3:]:  # 显示最近3个
                            print(f"   - {req.get('type', 'unknown')}: {req.get('url', 'unknown')}")
                    
                    print("🎉 HTTP 拦截在真实场景中工作正常！")
                else:
                    print("⚠️ 未检测到 Verisoul 请求，可能页面未加载 Verisoul 或域名需要更新")
            else:
                print("❌ 无法获取拦截统计")
        else:
            print("❌ 页面导航失败")
            
        automation.cleanup()
        
    except Exception as e:
        print(f"❌ 真实场景测试失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 Verisoul HTTP 拦截功能测试")
    print("=" * 50)
    
    # 基础功能测试
    basic_success = test_http_interception()
    
    # 真实场景测试
    real_success = test_interception_in_real_scenario()
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"   基础功能测试: {'✅ 通过' if basic_success else '❌ 失败'}")
    print(f"   真实场景测试: {'✅ 通过' if real_success else '❌ 失败'}")
    
    if basic_success and real_success:
        print("\n🎉 所有测试通过！HTTP 拦截功能工作正常")
        print("\n📋 使用方法:")
        print("   python drissionpage_automation.py")
        print("   或")
        print("   python run_drissionpage_verification.py")
        print("\n🎯 HTTP 拦截器会自动:")
        print("   - 拦截所有 Verisoul 请求")
        print("   - 返回伪造的成功响应")
        print("   - 显示拦截统计信息")
    else:
        print("\n❌ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
