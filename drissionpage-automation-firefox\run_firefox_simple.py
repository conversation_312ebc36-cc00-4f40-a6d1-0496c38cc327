#!/usr/bin/env python3
"""
Firefox Simple Automation
简化的 Firefox 自动化流程，专注于核心功能
"""

import sys
import time
from pathlib import Path

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

from drissionpage_automation import FirefoxAutomation
from handlers import AugmentAuth, OneMailHandler

def run_firefox_simple():
    """简化的 Firefox 自动化流程"""
    print('🦊 Firefox 简化自动化流程')
    print('=' * 40)
    
    automation = None
    
    try:
        # 步骤1: 初始化
        print('🔧 步骤1: 初始化组件...')
        automation = FirefoxAutomation()
        augment_auth = AugmentAuth()
        onemail_handler = OneMailHandler()
        
        # 步骤2: 生成授权URL
        print('🔐 步骤2: 生成授权 URL...')
        auth_url = augment_auth.generate_auth_url()
        print(f'授权 URL: {auth_url}')
        
        # 步骤3: 生成临时邮箱
        print('📧 步骤3: 生成临时邮箱...')
        temp_email = onemail_handler.generate_email()
        if not temp_email:
            raise Exception('临时邮箱生成失败')
        print(f'临时邮箱: {temp_email}')
        
        # 步骤4: 启动浏览器
        print('🦊 步骤4: 启动 Firefox 浏览器...')
        automation.init_browser()
        
        # 步骤5: 导航到授权页面
        print('🌐 步骤5: 导航到授权页面...')
        automation.driver.get(auth_url)
        time.sleep(5)
        
        current_url = automation.driver.current_url
        print(f'当前 URL: {current_url}')
        
        # 步骤6: 输入邮箱
        print('📧 步骤6: 输入邮箱...')
        
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # 查找邮箱输入框
        email_input = WebDriverWait(automation.driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="username"], input[id="username"], input[type="email"]'))
        )
        
        # 输入邮箱
        email_input.clear()
        email_input.send_keys(temp_email)
        
        # 验证输入
        value = email_input.get_attribute('value')
        print(f'已输入邮箱: {value}')
        
        # 步骤7: 查找并点击 Continue 按钮
        print('🔄 步骤7: 点击 Continue 按钮...')
        
        # 查找 Continue 按钮
        continue_button = WebDriverWait(automation.driver, 10).until(
            EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"], button[name="action"], input[type="submit"]'))
        )
        
        # 点击按钮
        continue_button.click()
        time.sleep(3)
        
        # 检查页面变化
        new_url = automation.driver.current_url
        print(f'点击后 URL: {new_url}')
        
        # 步骤8: 等待验证码页面
        print('⏳ 步骤8: 等待验证码页面...')
        time.sleep(5)
        
        # 检查是否进入验证码页面
        try:
            code_input = WebDriverWait(automation.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[name="code"], input[id="code"]'))
            )
            print('✅ 已进入验证码页面')
        except:
            print('⚠️ 未检测到验证码页面，可能需要处理验证码或其他步骤')
            
            # 检查当前页面内容
            title = automation.driver.title
            print(f'当前页面标题: {title}')
            
            # 保存截图用于调试
            automation.driver.save_screenshot('debug_current_page.png')
            print('📸 已保存调试截图: debug_current_page.png')
        
        # 步骤9: 获取验证码（如果进入了验证码页面）
        if 'code_input' in locals():
            print('📨 步骤9: 获取邮箱验证码...')
            
            # 等待邮件发送
            import random
            wait_seconds = random.randint(8, 15)
            print(f'⏰ 等待邮件发送（{wait_seconds}秒）...')
            time.sleep(wait_seconds)
            
            # 获取验证码
            verification_code = onemail_handler.get_verification_code(temp_email, 2)
            
            if verification_code:
                print(f'✅ 获取到验证码: {verification_code}')
                
                # 输入验证码
                code_input.clear()
                code_input.send_keys(verification_code)
                
                # 查找并点击验证码页面的 Continue 按钮
                verify_button = WebDriverWait(automation.driver, 10).until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"], button[name="action"]'))
                )
                verify_button.click()
                time.sleep(5)
                
                # 检查最终结果
                final_url = automation.driver.current_url
                print(f'验证后 URL: {final_url}')
                
                if 'code=' in final_url:
                    print('🎉 成功获取授权码！')
                    # 提取授权码
                    import re
                    match = re.search(r'code=([^&]+)', final_url)
                    if match:
                        auth_code = match.group(1)
                        print(f'授权码: {auth_code}')
                else:
                    print('⚠️ 未检测到授权码，可能需要进一步处理')
            else:
                print('❌ 未能获取到验证码')
        
        print('\n🎉 Firefox 简化自动化流程完成！')
        print('🔍 请检查浏览器窗口查看最终状态')
        
        # 保持浏览器打开
        input('\n按 Enter 键关闭浏览器...')
        
    except KeyboardInterrupt:
        print('\n⚠️ 用户中断操作')
    except Exception as e:
        print(f'❌ 自动化流程失败: {e}')
        import traceback
        traceback.print_exc()
        
        # 保存调试信息
        if automation and automation.driver:
            try:
                automation.driver.save_screenshot('error_screenshot.png')
                print('📸 已保存错误截图: error_screenshot.png')
            except:
                pass
    finally:
        # 清理资源
        if automation and automation.driver:
            try:
                print('🧹 关闭浏览器...')
                automation.driver.quit()
            except Exception as e:
                print(f'⚠️ 关闭浏览器时出错: {e}')
        
        print('👋 程序结束')

if __name__ == '__main__':
    run_firefox_simple()
