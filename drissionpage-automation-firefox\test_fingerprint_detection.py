#!/usr/bin/env python3
"""
浏览器指纹检测测试脚本
测试各种指纹检测网站，验证防护效果
"""

import sys
import os
import time
from pathlib import Path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from drissionpage_automation import DrissionPageAutomation

def test_fingerprint_detection_sites():
    """测试多个指纹检测网站"""
    print("🔍 浏览器指纹检测测试")
    print("=" * 60)
    
    # 指纹检测网站列表
    test_sites = [
        {
            'name': 'Bot.Sannysoft',
            'url': 'https://bot.sannysoft.com/',
            'description': '专业的自动化检测网站'
        },
        {
            'name': 'BrowserLeaks Canvas',
            'url': 'https://browserleaks.com/canvas',
            'description': 'Canvas 指纹检测'
        },
        {
            'name': 'BrowserLeaks WebGL',
            'url': 'https://browserleaks.com/webgl',
            'description': 'WebGL 指纹检测'
        },
        {
            'name': 'AmIUnique',
            'url': 'https://amiunique.org/',
            'description': '综合指纹唯一性检测'
        },
        {
            'name': 'Device Info',
            'url': 'https://www.deviceinfo.me/',
            'description': '设备信息检测'
        }
    ]
    
    automation = None
    results = {}
    
    try:
        # 初始化自动化实例
        print("🚀 初始化 DrissionPage 自动化...")
        automation = DrissionPageAutomation()
        automation.init_browser()
        print("✅ 浏览器启动成功")
        
        # 检查指纹防护状态
        if automation.config.fingerprint_protection:
            print("✅ 指纹防护已启用")
        else:
            print("⚠️ 指纹防护已禁用")
            return False
        
        # 逐个测试网站
        for i, site in enumerate(test_sites, 1):
            print(f"\n📋 测试 {i}/{len(test_sites)}: {site['name']}")
            print(f"   描述: {site['description']}")
            print(f"   URL: {site['url']}")
            
            try:
                # 导航到测试网站
                automation.navigate_to_page(site['url'])
                
                # 等待页面加载
                time.sleep(5)
                
                # 获取页面标题
                title = automation.page.title
                print(f"   ✅ 页面加载成功: {title}")
                
                # 执行特定的检测测试
                site_results = perform_site_specific_tests(automation, site)
                results[site['name']] = site_results
                
                # 短暂等待
                time.sleep(2)
                
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
                results[site['name']] = {'error': str(e)}
        
        # 生成测试报告
        generate_test_report(results)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程失败: {e}")
        return False
    finally:
        if automation and automation.page:
            print("\n🧹 清理资源...")
            automation.cleanup()

def perform_site_specific_tests(automation, site):
    """执行特定网站的检测测试"""
    results = {'site': site['name'], 'tests': {}}
    
    try:
        if 'bot.sannysoft' in site['url']:
            # Bot.Sannysoft 特定测试
            results['tests'] = test_bot_sannysoft(automation)
        elif 'browserleaks.com/canvas' in site['url']:
            # Canvas 指纹测试
            results['tests'] = test_canvas_fingerprint(automation)
        elif 'browserleaks.com/webgl' in site['url']:
            # WebGL 指纹测试
            results['tests'] = test_webgl_fingerprint(automation)
        elif 'amiunique.org' in site['url']:
            # 综合指纹测试
            results['tests'] = test_comprehensive_fingerprint(automation)
        elif 'deviceinfo.me' in site['url']:
            # 设备信息测试
            results['tests'] = test_device_info(automation)
        else:
            # 通用测试
            results['tests'] = test_generic_fingerprint(automation)
            
    except Exception as e:
        results['tests'] = {'error': str(e)}
    
    return results

def test_bot_sannysoft(automation):
    """测试 Bot.Sannysoft 检测"""
    print("   🤖 执行自动化检测测试...")
    
    try:
        # 检查页面中的检测结果
        detection_results = automation.page.run_js("""
            const results = {};
            
            // 查找检测结果元素
            const rows = document.querySelectorAll('tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 2) {
                    const test = cells[0].textContent.trim();
                    const result = cells[1].textContent.trim();
                    if (test && result) {
                        results[test] = result;
                    }
                }
            });
            
            return results;
        """)
        
        print(f"   📊 检测结果: {len(detection_results)} 项测试")
        for test, result in detection_results.items():
            status = "✅" if "passed" in result.lower() or "ok" in result.lower() else "⚠️"
            print(f"     {status} {test}: {result}")
        
        return detection_results
        
    except Exception as e:
        print(f"   ❌ Bot.Sannysoft 测试失败: {e}")
        return {'error': str(e)}

def test_canvas_fingerprint(automation):
    """测试 Canvas 指纹"""
    print("   🎨 执行 Canvas 指纹测试...")
    
    try:
        # 生成多个 Canvas 指纹
        fingerprints = []
        for i in range(3):
            fingerprint = automation.page.run_js("""
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Canvas Test', 2, 2);
                return canvas.toDataURL();
            """)
            fingerprints.append(fingerprint[:50] + '...')
            time.sleep(0.5)
        
        unique_count = len(set(fingerprints))
        print(f"   📊 生成了 {unique_count} 个不同的 Canvas 指纹")
        
        return {
            'fingerprints': fingerprints,
            'unique_count': unique_count,
            'protection_effective': unique_count > 1
        }
        
    except Exception as e:
        print(f"   ❌ Canvas 指纹测试失败: {e}")
        return {'error': str(e)}

def test_webgl_fingerprint(automation):
    """测试 WebGL 指纹"""
    print("   🎮 执行 WebGL 指纹测试...")
    
    try:
        webgl_info = automation.page.run_js("""
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) return { error: 'WebGL not supported' };
            
            return {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER),
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
            };
        """)
        
        print(f"   📊 WebGL 信息:")
        print(f"     供应商: {webgl_info.get('vendor', 'N/A')}")
        print(f"     渲染器: {webgl_info.get('renderer', 'N/A')}")
        
        return webgl_info
        
    except Exception as e:
        print(f"   ❌ WebGL 指纹测试失败: {e}")
        return {'error': str(e)}

def test_comprehensive_fingerprint(automation):
    """测试综合指纹"""
    print("   🔍 执行综合指纹测试...")
    
    try:
        # 等待页面分析完成
        time.sleep(10)
        
        # 尝试获取唯一性分数
        uniqueness_info = automation.page.run_js("""
            // 查找唯一性相关信息
            const uniquenessElements = document.querySelectorAll('[class*="uniqueness"], [id*="uniqueness"], [class*="score"], [id*="score"]');
            const results = {};
            
            uniquenessElements.forEach(el => {
                if (el.textContent) {
                    results[el.className || el.id || 'unknown'] = el.textContent.trim();
                }
            });
            
            return results;
        """)
        
        print(f"   📊 唯一性分析: {len(uniqueness_info)} 项指标")
        
        return uniqueness_info
        
    except Exception as e:
        print(f"   ❌ 综合指纹测试失败: {e}")
        return {'error': str(e)}

def test_device_info(automation):
    """测试设备信息"""
    print("   📱 执行设备信息测试...")
    
    try:
        device_info = automation.page.run_js("""
            return {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                screenResolution: screen.width + 'x' + screen.height,
                colorDepth: screen.colorDepth,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                hardwareConcurrency: navigator.hardwareConcurrency
            };
        """)
        
        print(f"   📊 设备信息:")
        print(f"     平台: {device_info.get('platform', 'N/A')}")
        print(f"     屏幕: {device_info.get('screenResolution', 'N/A')}")
        print(f"     语言: {device_info.get('language', 'N/A')}")
        
        return device_info
        
    except Exception as e:
        print(f"   ❌ 设备信息测试失败: {e}")
        return {'error': str(e)}

def test_generic_fingerprint(automation):
    """通用指纹测试"""
    print("   🔧 执行通用指纹测试...")
    
    try:
        generic_info = automation.page.run_js("""
            return {
                webdriver: navigator.webdriver,
                chrome: !!window.chrome,
                plugins: navigator.plugins.length,
                mimeTypes: navigator.mimeTypes.length,
                cookieEnabled: navigator.cookieEnabled,
                doNotTrack: navigator.doNotTrack
            };
        """)
        
        print(f"   📊 通用信息:")
        print(f"     WebDriver: {generic_info.get('webdriver', 'N/A')}")
        print(f"     Chrome 对象: {generic_info.get('chrome', 'N/A')}")
        print(f"     插件数量: {generic_info.get('plugins', 'N/A')}")
        
        return generic_info
        
    except Exception as e:
        print(f"   ❌ 通用指纹测试失败: {e}")
        return {'error': str(e)}

def generate_test_report(results):
    """生成测试报告"""
    print(f"\n📊 指纹检测测试报告")
    print("=" * 60)
    
    total_sites = len(results)
    successful_sites = sum(1 for r in results.values() if 'error' not in r)
    
    print(f"测试网站总数: {total_sites}")
    print(f"成功测试: {successful_sites}")
    print(f"失败测试: {total_sites - successful_sites}")
    
    print(f"\n📋 详细结果:")
    for site_name, site_results in results.items():
        if 'error' in site_results:
            print(f"❌ {site_name}: {site_results['error']}")
        else:
            print(f"✅ {site_name}: 测试完成")
            if 'tests' in site_results:
                tests = site_results['tests']
                if isinstance(tests, dict) and 'protection_effective' in tests:
                    effectiveness = "有效" if tests['protection_effective'] else "无效"
                    print(f"   指纹防护: {effectiveness}")

def main():
    """主测试函数"""
    print("🧪 浏览器指纹检测测试套件")
    print("=" * 70)
    
    success = test_fingerprint_detection_sites()
    
    print(f"\n🎯 测试总结")
    print("=" * 70)
    
    if success:
        print("🎉 指纹检测测试完成！")
        print("\n💡 建议:")
        print("   1. 定期运行此测试验证防护效果")
        print("   2. 关注新的指纹检测技术")
        print("   3. 根据测试结果调整防护策略")
        print("   4. 监控目标网站的检测变化")
    else:
        print("⚠️ 测试过程中出现问题")
        print("   请检查配置和网络连接")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
