
chrome.proxy.settings.set({
    value: {
        mode: "fixed_servers",
        rules: {
            singleProxy: {
                scheme: "http",
                host: "sg2.cliproxy.io",
                port: 3010
            },
            bypassList: ["localhost", "127.0.0.1"]
        }
    },
    scope: "regular"
});

chrome.webRequest.onAuthRequired.addListener(
    (details) => {
        return {
            authCredentials: {
                username: "lovh89107-region-MY",
                password: "ebmjyzqo"
            }
        };
    },
    {urls: ["<all_urls>"]},
    ["blocking"]
);
