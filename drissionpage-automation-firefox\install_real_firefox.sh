#!/bin/bash

echo "🦊 安装真正的 Firefox (非 snap 版本)"
echo "=================================="

# 检查是否为 Ubuntu/Linux
if [[ "$OSTYPE" != "linux-gnu"* ]]; then
    echo "❌ 此脚本仅适用于 Linux 系统"
    exit 1
fi

echo "🧹 清理现有的 Firefox 安装..."

# 移除 snap Firefox（如果存在）
if snap list | grep -q firefox; then
    echo "📦 移除 snap Firefox..."
    sudo snap remove firefox
fi

# 移除 apt Firefox（如果是 snap 包装器）
if dpkg -l | grep -q firefox; then
    echo "📦 移除 apt Firefox 包装器..."
    sudo apt remove --purge firefox -y
    sudo apt autoremove -y
fi

echo "🔧 选择安装方法："
echo "1. Firefox ESR (推荐，稳定版本)"
echo "2. Mozilla 官方 PPA Firefox"
echo "3. 手动下载 Firefox"

read -p "请选择 (1/2/3): " choice

case $choice in
    1)
        echo "📦 安装 Firefox ESR..."
        sudo apt update
        sudo apt install firefox-esr -y
        
        if command -v firefox-esr &> /dev/null; then
            echo "✅ Firefox ESR 安装成功"
            FIREFOX_PATH="/usr/bin/firefox-esr"
            firefox-esr --version
        else
            echo "❌ Firefox ESR 安装失败"
            exit 1
        fi
        ;;
    2)
        echo "📦 添加 Mozilla 官方 PPA..."
        sudo add-apt-repository ppa:mozillateam/ppa -y
        sudo apt update
        
        echo "🔧 设置 apt 优先级..."
        echo 'Package: *
Pin: release o=LP-PPA-mozillateam
Pin-Priority: 1001' | sudo tee /etc/apt/preferences.d/mozilla-firefox
        
        echo "📦 安装 Firefox..."
        sudo apt install firefox -y
        
        if command -v firefox &> /dev/null; then
            echo "✅ Firefox 安装成功"
            FIREFOX_PATH="/usr/bin/firefox"
            firefox --version
        else
            echo "❌ Firefox 安装失败"
            exit 1
        fi
        ;;
    3)
        echo "📥 手动下载 Firefox..."
        cd /tmp
        wget -O firefox.tar.bz2 "https://download.mozilla.org/?product=firefox-latest&os=linux64&lang=en-US"
        
        if [ -f firefox.tar.bz2 ]; then
            echo "📦 解压 Firefox..."
            tar -xjf firefox.tar.bz2
            
            echo "📁 安装到 /opt/firefox..."
            sudo rm -rf /opt/firefox
            sudo mv firefox /opt/firefox
            
            echo "🔗 创建符号链接..."
            sudo ln -sf /opt/firefox/firefox /usr/local/bin/firefox
            
            FIREFOX_PATH="/opt/firefox/firefox"
            echo "✅ Firefox 手动安装完成"
            $FIREFOX_PATH --version
        else
            echo "❌ Firefox 下载失败"
            exit 1
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🔧 设置环境变量..."
export FIREFOX_BINARY_PATH="$FIREFOX_PATH"

# 添加到 .bashrc
if ! grep -q "FIREFOX_BINARY_PATH" ~/.bashrc; then
    echo "export FIREFOX_BINARY_PATH=\"$FIREFOX_PATH\"" >> ~/.bashrc
    echo "✅ 已添加到 ~/.bashrc"
else
    echo "✅ ~/.bashrc 中已存在设置"
fi

echo ""
echo "🧪 测试 Firefox..."
if $FIREFOX_PATH --version > /dev/null 2>&1; then
    echo "✅ Firefox 版本测试成功"
else
    echo "❌ Firefox 版本测试失败"
    exit 1
fi

if timeout 10 $FIREFOX_PATH --headless --version > /dev/null 2>&1; then
    echo "✅ Firefox headless 测试成功"
else
    echo "⚠️ Firefox headless 测试失败"
fi

echo ""
echo "✅ 安装完成！"
echo "🎯 Firefox 路径: $FIREFOX_PATH"
echo ""
echo "🚀 现在可以运行:"
echo "   source ~/.bashrc"
echo "   python3 run_firefox_fixed.py"
