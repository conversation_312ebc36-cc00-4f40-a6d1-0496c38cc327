#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

const TOKENS_FILE = path.join(__dirname, '../tokens.json');
const CONFIG_FILE = path.join(__dirname, 'config.json');

// Load configuration
let config;
try {
  config = JSON.parse(fs.readFileSync(CONFIG_FILE, 'utf8'));
} catch (error) {
  console.error('Error loading config file:', error);
  process.exit(1);
}

const DB_PATH = path.join(__dirname, config.database.path);

console.log('🔄 Starting token migration from tokens.json to SQLite...\n');

// Initialize SQLite database
const db = new sqlite3.Database(DB_PATH);

// Create table if not exists
db.serialize(() => {
  db.run(`CREATE TABLE IF NOT EXISTS tokens (
    id TEXT PRIMARY KEY,
    access_token TEXT NOT NULL,
    tenant_url TEXT NOT NULL,
    description TEXT,
    email_note TEXT,
    user_agent TEXT,
    session_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    used BOOLEAN DEFAULT 0,
    created_timestamp INTEGER
  )`);
});

// Read tokens from JSON file
let tokens = [];
if (fs.existsSync(TOKENS_FILE)) {
  try {
    const data = fs.readFileSync(TOKENS_FILE, 'utf8');
    tokens = JSON.parse(data);
    console.log(`📁 Found ${tokens.length} tokens in tokens.json`);
  } catch (error) {
    console.error('Error reading tokens.json:', error);
    process.exit(1);
  }
} else {
  console.log('⚠️  tokens.json file not found, nothing to migrate');
  process.exit(0);
}

// Migrate tokens
let migratedCount = 0;
let skippedCount = 0;

const migrateToken = (token) => {
  return new Promise((resolve, reject) => {
    // Check if token already exists
    db.get('SELECT id FROM tokens WHERE id = ?', [token.id], (err, row) => {
      if (err) {
        reject(err);
        return;
      }
      
      if (row) {
        console.log(`⏭️  Skipping token ${token.id} (already exists)`);
        skippedCount++;
        resolve();
        return;
      }
      
      // Extract metadata
      const metadata = token.metadata || {};
      const userAgent = metadata.user_agent || 'unknown';
      const sessionId = metadata.session_id || null;
      const emailNote = token.email_note || null;
      const description = token.description || 'Migrated from tokens.json';
      
      // Convert timestamp
      const createdAt = token.createdTime || 
                       (token.createdTimestamp ? new Date(token.createdTimestamp).toISOString() : new Date().toISOString());
      
      // Insert token
      db.run(
        `INSERT INTO tokens (id, access_token, tenant_url, description, email_note, user_agent, session_id, created_at, used, created_timestamp)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          token.id,
          token.access_token,
          token.tenant_url,
          description,
          emailNote,
          userAgent,
          sessionId,
          createdAt,
          token.used ? 1 : 0,
          token.createdTimestamp || Date.now()
        ],
        function(insertErr) {
          if (insertErr) {
            reject(insertErr);
            return;
          }
          
          console.log(`✅ Migrated token ${token.id}`);
          migratedCount++;
          resolve();
        }
      );
    });
  });
};

// Process all tokens
async function migrateAllTokens() {
  try {
    for (const token of tokens) {
      await migrateToken(token);
    }
    
    console.log(`\n🎉 Migration completed!`);
    console.log(`   Migrated: ${migratedCount} tokens`);
    console.log(`   Skipped: ${skippedCount} tokens (already existed)`);
    console.log(`   Total: ${tokens.length} tokens processed`);
    
    // Verify migration
    db.get('SELECT COUNT(*) as count FROM tokens', (err, row) => {
      if (err) {
        console.error('Error verifying migration:', err);
      } else {
        console.log(`   Database now contains: ${row.count} tokens`);
      }
      
      db.close();
    });
    
  } catch (error) {
    console.error('Migration failed:', error);
    db.close();
    process.exit(1);
  }
}

migrateAllTokens();
